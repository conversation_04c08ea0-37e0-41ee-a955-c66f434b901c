<template>
	<view class="profile-page">
		<!-- 下拉刷新 -->
		<scroll-view
			class="scroll-container"
			scroll-y="true"
			refresher-enabled="true"
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
		>
		<!-- 顶部用户信息 - 仅登录后显示 -->
		<view v-if="isLoggedIn" class="header-section">
			<!-- 第一排：用户基本信息 -->
			<view class="user-basic-info">
				<view class="user-avatar" @click="handleAvatarClick">
					<image class="avatar-img" :src="userInfo.avatar || '/static/images/znt_avatar.png'"></image>
					<view class="avatar-border"></view>
					<view class="avatar-edit-hint">
						<text class="edit-icon">📷</text>
					</view>
				</view>
				<view class="user-text-info">
					<text class="username">{{ userInfo.nickname || userInfo.username }}</text>
					<text class="user-uid">UID: {{ userInfo.id || '未知' }}</text>
				</view>
			</view>

			<!-- 第二排：余额统计信息 -->
			<view class="user-stats-section">
				<view class="stats-container">
					<view class="stat-item">
						<text class="stat-value">{{ userInfo.balance || 0 }}</text>
						<text class="stat-label">余额</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="stat-value">{{ userInfo.points || 0 }}</text>
						<text class="stat-label">点数</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="stat-value">{{ userInfo.inviteCount || 0 }}</text>
						<text class="stat-label">邀请</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 未登录状态的用户信息 -->
		<view v-else class="unauth-user-section">
			<view class="unauth-user-info">
				<view class="unauth-avatar" @click="showLoginModal">
					<image class="unauth-avatar-img" src="/static/images/znt_avatar.png"></image>
				</view>
				<view class="unauth-text" @click="showLoginModal">
					<text class="unauth-title">登录/注册</text>
					<text class="unauth-desc">成为本站会员</text>
					<!-- 测试登录按钮 -->
					<!-- <button class="test-login-btn" @click.stop="testLogin">测试登录</button> -->
				</view>
			</view>
		</view>

		<!-- AI Mate 会员卡片 -->
		<view class="member-card">
			<view class="card-content">
				<view class="card-left">
					<text class="card-title">{{ isLoggedIn ? '成为合伙人 ✨' : '启元AI +' }}</text>
					<text class="card-desc">{{ isLoggedIn ? '邀请好友可获得高额佣金' : '邀请好友自动开通合伙人' }}</text>
				</view>
				<view class="card-right">
					<button class="upgrade-btn" @click="upgradeMembership">{{ isLoggedIn ? '立即推广' : '开启合伙' }}</button>
				</view>
			</view>
		</view>

		<!-- 功能模块 -->
		<view class="function-modules">
			<view class="modules-container">
				<!-- 第一排 -->
				<view class="module-row">
					<view class="module-item" @click="navigateToModule('wallet')">
						<view class="item-icon">
							<text class="icon-emoji">💰</text>
						</view>
						<text class="item-title">钱包</text>
						<text class="item-desc">余额管理</text>
					</view>

					<view class="module-item" @click="navigateToModule('points')">
						<view class="item-icon">
							<text class="icon-emoji">💎</text>
						</view>
						<text class="item-title">点数</text>
						<text class="item-desc">积分明细</text>
					</view>

					<view class="module-item" @click="navigateToModule('packages')">
						<view class="item-icon">
							<text class="icon-emoji">📦</text>
						</view>
						<text class="item-title">套餐</text>
						<text class="item-desc">套餐明细</text>
					</view>
				</view>

				<!-- 第二排 -->
				<view class="module-row">
					<view class="module-item" @click="navigateToModule('team')">
						<view class="item-icon">
							<text class="icon-emoji">👥</text>
						</view>
						<text class="item-title">团队</text>
						<text class="item-desc">团队管理</text>
					</view>

					<view class="module-item" @click="navigateToModule('customer-service')">
						<view class="item-icon">
							<text class="icon-emoji">💬</text>
						</view>
						<text class="item-title">客服</text>
						<text class="item-desc">联系客服</text>
					</view>

					<view class="module-item" @click="navigateToModule('settings')">
						<view class="item-icon">
							<text class="icon-emoji">⚙️</text>
						</view>
						<text class="item-title">设置</text>
						<text class="item-desc">系统设置</text>
					</view>
				</view>
			</view>
		</view>

		</scroll-view>
	</view>

	<!-- 底部导航栏 -->
	<MagicNavigation
		:items="navItems"
		:current="currentNavIndex"
		@change="handleNavChange"
	/>

	<!-- 智能登录弹窗 -->
	<smart-login-modal
		:visible="showLoginModalFlag"
		@close="hideLoginModal"
		@login-success="handleLoginSuccess"
	></smart-login-modal>

	<!-- 设置卡片弹窗 -->
	<view v-if="showSettingsModal" class="settings-modal-overlay" @click="closeSettingsModal">
		<view class="settings-modal-card" @click.stop>
			<view class="settings-modal-header">
				<text class="settings-modal-title">设置</text>
				<view class="settings-modal-close" @click="closeSettingsModal">
					<text class="close-icon">✕</text>
				</view>
			</view>

			<view class="settings-modal-content">
				<view class="settings-option" @click="handlePersonalSettings">
					<view class="option-icon">
						<text class="icon-emoji">👤</text>
					</view>
					<view class="option-content">
						<text class="option-title">个人设置</text>
						<text class="option-desc">修改个人信息</text>
					</view>
					<view class="option-arrow">
						<text class="arrow-icon">›</text>
					</view>
				</view>

				<view class="settings-option" @click="handleAccountSecurity">
					<view class="option-icon">
						<text class="icon-emoji">🔒</text>
					</view>
					<view class="option-content">
						<text class="option-title">账户安全</text>
						<text class="option-desc">密码与安全设置</text>
					</view>
					<view class="option-arrow">
						<text class="arrow-icon">›</text>
					</view>
				</view>

				<view class="settings-option logout-option" @click="handleLogout">
					<view class="option-icon">
						<text class="icon-emoji">🚪</text>
					</view>
					<view class="option-content">
						<text class="option-title">退出登录</text>
						<text class="option-desc">安全退出当前账户</text>
					</view>
					<view class="option-arrow">
						<text class="arrow-icon">›</text>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 钱包管理弹窗 -->
	<view v-if="showWalletModal" class="wallet-modal-overlay" @click="closeWalletModal">
		<view class="wallet-modal-card" @click.stop>
			<view class="wallet-modal-header">
				<text class="wallet-modal-title">钱包管理</text>
				<view class="wallet-modal-close" @click="closeWalletModal">
					<text class="close-icon">✕</text>
				</view>
			</view>
			<view class="wallet-modal-content">
				<!-- 错误信息显示 -->
				<view v-if="errorMessage" class="error-message">
					<text class="error-text">{{ errorMessage }}</text>
				</view>

				<!-- 当前余额显示 -->
				<view class="wallet-balance-section">
					<view class="balance-header">
						<text class="balance-label">当前余额</text>
						<view class="withdraw-bubble" @click.stop="handleWithdraw">
							<text class="bubble-text">立即提现</text>
						</view>
					</view>
					<text class="balance-amount">¥{{ userInfo.balance || 0 }}</text>
					<text class="balance-desc">来自邀请用户充值分成</text>
				</view>

				<!-- 记录选项卡 -->
				<view class="records-tabs">
					<view class="tab-item" :class="{ active: activeTab === 'balance' }" @click="switchTab('balance')">
						<text class="tab-text">余额明细</text>
					</view>
					<view class="tab-item" :class="{ active: activeTab === 'withdraw' }" @click="switchTab('withdraw')">
						<text class="tab-text">提现记录</text>
					</view>
				</view>

				<!-- 余额明细记录 -->
				<view v-if="activeTab === 'balance'" class="balance-history-section">
					<view class="history-list">
						<view v-if="balanceLoading" class="loading-history">
							<text class="loading-text">加载中...</text>
						</view>
						<view v-else-if="balanceHistory.length === 0" class="empty-history">
							<text class="empty-text">暂无明细记录</text>
						</view>
						<view v-else v-for="(item, index) in balanceHistory" :key="index" class="history-item">
							<view class="history-info">
								<text class="history-type">{{ item.description }}</text>
								<text class="history-time">{{ item.time }}</text>
							</view>
							<text class="history-amount" :class="item.amount > 0 ? 'income' : 'expense'">
								{{ item.amount > 0 ? '+' : '' }}¥{{ item.amount }}
							</text>
						</view>
					</view>
				</view>

				<!-- 提现记录 -->
				<view v-if="activeTab === 'withdraw'" class="withdraw-history-section">
					<view class="history-list">
						<view v-if="withdrawLoading" class="loading-history">
							<text class="loading-text">加载中...</text>
						</view>
						<view v-else-if="withdrawHistory.length === 0" class="empty-history">
							<text class="empty-text">暂无提现记录</text>
						</view>
						<view v-else v-for="(item, index) in withdrawHistory" :key="index" class="withdraw-item">
							<view class="withdraw-info">
								<text class="withdraw-type">{{ item.withdrawType }}</text>
								<text class="withdraw-time">{{ item.time }}</text>
							</view>

							<!-- 三列布局：左侧提现方式，中间驳回原因，右侧金额和状态 -->
							<view class="withdraw-row">
								<!-- 左侧已经有提现方式，这里不需要内容 -->
								<view class="withdraw-left"></view>

								<!-- 中间显示驳回原因 -->
								<view class="withdraw-middle" v-if="item.rejectReason">
									<view class="reject-reason">
										<text class="reject-reason-text">{{ item.rejectReason }}</text>
									</view>
								</view>
								<view class="withdraw-middle" v-else></view>

								<!-- 右侧显示金额和状态 -->
								<view class="withdraw-right">
									<text class="withdraw-amount">¥{{ item.amount }}</text>
									<text class="withdraw-status" :class="item.statusClass">{{ item.status }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 提现表单 -->
				<view v-if="activeTab === 'withdrawForm'" class="withdraw-form-section">
					<view class="withdraw-form">
						<!-- 提现金额显示 -->
						<view class="form-item">
							<text class="form-label">提现金额</text>
							<view class="amount-display">
								<text class="amount-text">¥{{ userInfo.balance || 0 }}</text>
								<text class="amount-note">（全额提现）</text>
							</view>
						</view>

						<!-- 手续费显示 -->
						<view class="form-item" v-if="partnerConfig && partnerConfig.withdrawalFee > 0">
							<text class="form-label">手续费</text>
							<text class="fee-text">{{ partnerConfig.withdrawalFee }}% (¥{{ calculateFee() }})</text>
						</view>

						<!-- 实际到账 -->
						<view class="form-item">
							<text class="form-label">实际到账</text>
							<text class="actual-amount">¥{{ calculateActualAmount() }}</text>
						</view>

						<!-- 支付宝账号输入（仅支付宝提现需要） -->
						<view v-if="partnerConfig && partnerConfig.withdrawalChannel === 'alipay'" class="form-item">
							<text class="form-label">支付宝账号</text>
							<input class="form-input" v-model="withdrawForm.account" placeholder="请输入支付宝账号（手机号或邮箱）" />
						</view>

						<!-- 真实姓名输入（仅支付宝提现需要） -->
						<view v-if="partnerConfig && partnerConfig.withdrawalChannel === 'alipay'" class="form-item">
							<text class="form-label">真实姓名</text>
							<input class="form-input" v-model="withdrawForm.name" placeholder="请输入支付宝账号对应的真实姓名" />
						</view>

						<!-- 微信提现说明 -->
						<view v-if="partnerConfig && partnerConfig.withdrawalChannel === 'merchant'" class="form-item">
							<view class="wechat-note">
								<text class="note-text">💚 微信提现将自动转账到您的微信零钱</text>
							</view>
						</view>

						<!-- 提现按钮 -->
						<view class="form-actions">
							<view class="action-button cancel-btn" @click="cancelWithdraw">取消</view>
							<view class="action-button submit-btn" @click="submitWithdrawInModal" :class="{ disabled: !canSubmit() }">
								{{ partnerConfig && partnerConfig.withdrawalChannel === 'merchant' ? '确认提现' : '申请提现' }}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 套餐管理弹窗 -->
	<view v-if="showPackageModal" class="wallet-modal-overlay" @click="closePackageModal">
		<view class="wallet-modal-card" @click.stop>
			<view class="wallet-modal-header">
				<text class="wallet-modal-title">套餐明细</text>
				<view class="wallet-modal-close" @click="closePackageModal">
					<text class="close-icon">✕</text>
				</view>
			</view>
			<view class="wallet-modal-content">
				<!-- 套餐记录 -->
				<view class="package-history-section">
					<view class="history-list">
						<view v-if="packageLoading" class="loading-history">
							<text class="loading-text">加载中...</text>
						</view>
						<view v-else-if="packageHistory.length === 0" class="empty-history">
							<text class="empty-text">暂无套餐记录</text>
						</view>
						<view v-else v-for="(item, index) in packageHistory" :key="index" class="package-item">
							<view class="package-header">
								<text class="package-name">{{ item.packageName }}</text>
								<text class="package-status" :class="item.status === '使用中' ? 'active' : 'expired'">{{ item.status }}</text>
							</view>
							<view class="package-details">
								<view class="package-detail-row">
									<text class="detail-label">订单号：</text>
									<text class="detail-value order-no">{{ item.orderNo }}</text>
								</view>
								<view class="package-detail-row">
									<text class="detail-label">购买金额：</text>
									<text class="detail-value package-amount">¥{{ item.amount }}</text>
								</view>
								<view class="package-detail-row">
									<text class="detail-label">开通时间：</text>
									<text class="detail-value">{{ item.startTime }}</text>
								</view>
								<view class="package-detail-row">
									<text class="detail-label">到期时间：</text>
									<text class="detail-value">{{ item.endTime }}</text>
								</view>
								<view class="package-detail-row">
									<text class="detail-label">总点数：</text>
									<text class="detail-value">{{ item.totalPoints }}点</text>
								</view>
								<view class="package-detail-row">
									<text class="detail-label">每日限制：</text>
									<text class="detail-value">{{ item.dailyLimit === 0 ? '不限制' : item.dailyLimit + '点' }}</text>
								</view>
								<view v-if="item.status === '使用中'" class="package-detail-row">
									<text class="detail-label">剩余天数：</text>
									<text class="detail-value remaining-days">{{ item.remainingDays }}天</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 点数弹窗 -->
	<view v-if="showPointsModal" class="wallet-modal-overlay" @click="closePointsModal">
		<view class="wallet-modal-card" @click.stop>
			<view class="wallet-modal-header">
				<text class="wallet-modal-title">点数明细</text>
				<view class="wallet-modal-close" @click="closePointsModal">
					<text class="close-icon">✕</text>
				</view>
			</view>
			<view class="wallet-modal-content">
				<view class="points-summary">
					<text class="points-label">当前点数</text>
					<text class="points-value">{{ getCurrentPoints() }}</text>
				</view>
				<view class="points-history">
					<view class="history-title">使用记录 ({{ pointsHistory.length }}条)</view>
					<view class="history-list">
						<view v-if="pointsLoading" class="loading-history">
							<text class="loading-text">加载中...</text>
						</view>
						<view v-else-if="pointsHistory.length === 0" class="empty-history">
							<text class="empty-text">暂无点数记录</text>
						</view>
						<view v-else v-for="(item, index) in pointsHistory" :key="index" class="points-item">
							<view class="points-info">
								<view class="points-type-row">
									<text class="points-type-icon">{{ getPointsTypeIcon(item.type) }}</text>
									<text class="points-type">{{ item.description }}</text>
								</view>
								<text class="points-time">{{ item.time }}</text>
								<text v-if="item.appTitle" class="app-title">{{ item.appTitle }}</text>
							</view>
							<view class="points-amount-section">
								<text class="points-amount" :class="item.amount > 0 ? 'income' : 'expense'">
									{{ item.amount > 0 ? '+' : '' }}{{ item.amount }}
								</text>
								<text v-if="item.balance" class="points-balance">余额: {{ item.balance }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 海报生成弹窗 -->
	<view v-if="showPosterModal" class="poster-modal-overlay" @tap="closePosterModal">
		<view class="poster-modal" @tap.stop>
			<view class="poster-modal-header">
				<text class="poster-modal-title">推广海报</text>
				<text class="poster-modal-close" @tap="closePosterModal">×</text>
			</view>

			<view class="poster-modal-content">


				<!-- 加载状态 -->
				<view v-if="posterGenerating" class="poster-loading">
					<text class="loading-text">正在生成海报...</text>
				</view>

				<!-- 海报预览 - 自适应显示 -->
				<view v-else-if="qrCodeUrl || inviteCode" class="poster-preview">
					<!-- 调试信息 -->
					<view class="debug-info" style="background: #f0f0f0; padding: 10px; margin-bottom: 10px; font-size: 12px;">
						<text>二维码URL: {{ qrCodeUrl ? '已生成' : '未生成' }}</text><br>
						<text>邀请码: {{ inviteCode || '未生成' }}</text><br>
						<text>背景图片: {{ posterConfig.posterbackgroundimage || '未设置' }}</text><br>
						<text>背景图片完整URL: {{ posterConfig.posterbackgroundimage ? getFullImageUrl(posterConfig.posterbackgroundimage) : '无' }}</text><br>
						<text>容器尺寸: {{ posterContainerStyle().width }} x {{ posterContainerStyle().height }} (新比例2:3)</text><br>
						<text>二维码尺寸: {{ posterConfig.qrCodeSize }}</text><br>
						<text>二维码位置: x={{ posterConfig.qrCodePosition.x }}, y={{ posterConfig.qrCodePosition.y }}</text>
					</view>

					<view class="poster-container" :style="posterContainerStyle">
						<!-- 默认背景（总是显示作为底层） -->
						<view class="default-background">
							<view class="background-content">
								<text class="app-title">AI智能体</text>
								<text class="invite-title">邀请您体验</text>
								<text class="invite-code">邀请码: {{ inviteCode }}</text>
							</view>
						</view>

						<!-- 背景图片（如果有的话，覆盖在默认背景上） -->
						<image
							v-if="posterConfig.posterbackgroundimage"
							:src="getFullImageUrl(posterConfig.posterbackgroundimage)"
							class="poster-background"
							mode="aspectFill"
							@error="onBackgroundImageError"
							@load="onBackgroundImageLoad"
						></image>

						<!-- 二维码叠加 -->
						<view
							class="qrcode-overlay"
							:style="qrCodeOverlayStyle()"
						>
							<image
								v-if="qrCodeUrl"
								:src="qrCodeUrl"
								class="qrcode-image"
								mode="aspectFit"
								@error="onQRCodeImageError"
								@load="onQRCodeImageLoad"
							></image>
							<!-- 二维码占位符 -->
							<view v-else class="qrcode-placeholder">
								<text class="placeholder-text">二维码</text>
								<text class="placeholder-code">{{ inviteCode || '生成中...' }}</text>
							</view>
						</view>
					</view>

					<!-- 邀请信息 -->
					<view class="invite-info">
						<view class="invite-item">
							<text class="invite-label">邀请码：</text>
							<text class="invite-value">{{ inviteCode }}</text>
							<text class="copy-btn" @tap="copyInviteCode">复制</text>
						</view>
						<view class="invite-item">
							<text class="invite-label">邀请链接：</text>
							<text class="invite-value">{{ inviteUrl }}</text>
							<text class="copy-btn" @tap="copyInviteUrl">复制</text>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view class="poster-actions">
						<button class="action-btn save-btn" @tap="savePosterToAlbum">保存到相册</button>
						<button class="action-btn share-btn" @tap="sharePoster">分享海报</button>
					</view>
				</view>

				<!-- 错误状态 -->
				<view v-else class="poster-error">
					<view class="error-icon">⚠️</view>
					<text class="error-text">海报生成失败</text>
					<text class="error-desc">请检查网络连接后重试</text>
					<button class="retry-btn" @tap="initPosterGeneration">重新生成</button>
				</view>
			</view>

			<!-- 隐藏的Canvas -->
			<!-- #ifdef H5 -->
			<canvas
				ref="posterCanvas"
				id="posterCanvas"
				:width="canvasWidth"
				:height="canvasHeight"
				:style="{width: canvasWidth + 'px', height: canvasHeight + 'px', position: 'fixed', left: '-2000px', top: '-2000px', zIndex: -999}"
			></canvas>
			<!-- #endif -->

			<!-- #ifndef H5 -->
			<canvas
				canvas-id="posterCanvas"
				:style="{width: canvasWidth + 'px', height: canvasHeight + 'px', position: 'fixed', left: '-2000px', top: '-2000px', zIndex: -999}"
			></canvas>
			<!-- #endif -->
		</view>
	</view>
</template>

<script>
import { userStore, memberAPI } from '@/api/members.js'
import { packageAPI, orderAPI } from '@/api/packages.js'
import SmartLoginModal from '@/components/smart-login-modal.vue'
import MagicNavigation from '@/components/MagicNavigation.vue'
import { API_BASE_URL, IMAGE_BASE_URL } from '@/config/index.js'

export default {
	name: 'ProfileIndex',
	components: {
		SmartLoginModal,
		MagicNavigation
	},
	data: function() {
		return {
			API_BASE_URL,
			IMAGE_BASE_URL,
			userInfo: {},
			isLoggedIn: false,
			refreshing: false,
			showLoginModalFlag: false,
			showSettingsModal: false, // 设置弹窗显示状态
			partnerConfig: null, // 合伙人配置
			showWalletModal: false, // 钱包弹窗显示状态
			showPointsModal: false, // 点数弹窗显示状态
			showPackageModal: false, // 套餐弹窗显示状态
			activeTab: 'balance', // 当前选中的标签页
			// 余额明细数据
			balanceHistory: [],
			balanceLoading: false,
			// 点数明细数据
			pointsHistory: [],
			pointsLoading: false,
			// 提现记录数据
			withdrawHistory: [],
			withdrawLoading: false,
			// 套餐记录数据
			packageHistory: [],
			packageLoading: false,
			// 提现表单数据
			withdrawForm: {
				account: '',
				name: ''
			},
			// 错误信息
			errorMessage: '',
			// 导航栏相关
			currentNavIndex: 4, // 个人中心对应索引4（吾界）
			navItems: [
				{ text: '初页', icon: 'icon-home', path: '/pages/index/index' },
				{ text: '同创', icon: 'icon-robot', path: '/pages/create/index' },
				{ text: '会享', icon: 'icon-message', path: '/pages/chat/index' },
				{ text: '往档', icon: 'icon-history', path: '/pages/history/index' },
				{ text: '吾界', icon: 'icon-user', path: '/pages/profile/index' }
			],
			// 海报生成相关
			showPosterModal: false,
			posterGenerating: false,
			posterImageUrl: '',
			qrCodeUrl: '',
			inviteCode: '',
			inviteUrl: '',
			// 二维码缓存
			qrCodeCache: {}, // 格式: { userId: { qrCodeUrl: '', inviteCode: '', inviteUrl: '', timestamp: 0 } }
			posterConfig: {
				backgroundImage: '', // 从后台配置加载
				posterbackgroundimage: '', // 模板中使用的属性名
				qrCodeSize: 80, // 默认尺寸，会被后台配置覆盖
				qrCodePosition: { x: 200, y: 400 } // 调整默认位置适应新高度
			},
			backgroundImageError: false,
			shareConfig: {
				title: 'AI智能体使用邀请',
				description: '注册即可获得额外免费使用次数，快来体验智能AI助手！'
			},
			canvasWidth: 375,
			canvasHeight: 667
		}
	},

	async onLoad() {
		this.checkLoginStatus()
		// 如果已登录，刷新用户信息
		if (this.isLoggedIn) {
			try {
				await this.refreshUserInfo()
			} catch (error) {
				console.error('页面加载时刷新用户信息失败:', error)
			}
		}
		// 加载合伙人配置
		this.loadPartnerConfig()
		// 预加载海报配置
		this.loadPosterConfig()
	},

	onShow() {
		this.checkLoginStatus()
		// 如果已登录，刷新用户信息
		if (this.isLoggedIn) {
			this.refreshUserInfo()
		}
	},
	methods: {
		// 获取完整的图片URL
		getFullImageUrl(imagePath) {
			if (!imagePath) {
				console.log('⚠️ getFullImageUrl: imagePath为空')
				return ''
			}
			// 如果已经是完整URL，直接返回
			if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
				console.log('🔗 getFullImageUrl: 已是完整URL:', imagePath)
				return imagePath
			}
			// 拼接完整URL
			const fullUrl = `${this.IMAGE_BASE_URL}${imagePath.startsWith('/') ? '' : '/'}${imagePath}`
			console.log('🔗 getFullImageUrl: 拼接URL:', { imagePath, IMAGE_BASE_URL: this.IMAGE_BASE_URL, fullUrl })
			return fullUrl
		},

		// 计算海报容器样式 - 自适应显示
		posterContainerStyle() {
			// 获取屏幕宽度，设置海报最大宽度为屏幕宽度的80%
			const screenWidth = uni.getSystemInfoSync().windowWidth
			const maxWidth = Math.min(screenWidth * 0.8, 350) // 最大不超过350px

			// 优先使用真实图片比例，如果没有则使用默认比例
			let aspectRatio = 2 / 3 // 默认比例（宽:高）

			// 如果已经获取到背景图片的真实比例，使用真实比例
			if (this.posterConfig.imageAspectRatio) {
				aspectRatio = this.posterConfig.imageAspectRatio
				console.log('🎯 使用真实图片比例:', aspectRatio)
			} else if (this.posterConfig.posterbackgroundimage) {
				// 如果有背景图片但还没获取到真实比例，使用默认比例
				aspectRatio = 2 / 3
				console.log('⏳ 背景图片加载中，使用默认比例:', aspectRatio)
			}

			const calculatedHeight = maxWidth / aspectRatio

			// 设置合理的最小和最大高度
			const minHeight = 300 // 降低最小高度
			const maxHeight = screenWidth * 1.5 // 最大高度不超过屏幕宽度的1.5倍
			const height = Math.max(minHeight, Math.min(calculatedHeight, maxHeight))

			console.log('📐 海报尺寸计算:', {
				screenWidth,
				maxWidth,
				aspectRatio,
				calculatedHeight,
				finalHeight: height,
				hasBackgroundImage: !!this.posterConfig.posterbackgroundimage,
				hasRealAspectRatio: !!this.posterConfig.imageAspectRatio
			})

			return {
				width: `${maxWidth}px`,
				height: `${height}px`,
				maxWidth: '90vw',
				margin: '0 auto'
			}
		},

		// 计算二维码叠加层样式
		qrCodeOverlayStyle() {
			const size = this.posterConfig.qrCodeSize || 80
			const position = this.posterConfig.qrCodePosition || { x: 200, y: 400 }

			console.log('🎯 计算二维码样式:', {
				size,
				position,
				posterConfig: this.posterConfig
			})

			// 获取当前容器尺寸（与 posterContainerStyle 保持一致）
			const screenWidth = uni.getSystemInfoSync().windowWidth
			const containerWidth = Math.min(screenWidth * 0.8, 350)
			const aspectRatio = 2 / 3
			const calculatedHeight = containerWidth / aspectRatio
			const minHeight = 500
			const containerHeight = Math.max(calculatedHeight, minHeight)

			// 简化位置计算逻辑
			let scaledX = position.x
			let scaledY = position.y
			let scaledSize = size

			// 如果配置的位置超出了当前容器，进行比例缩放
			if (position.x > containerWidth || position.y > containerHeight) {
				// 假设原始配置是基于400x600的画布
				const originalWidth = 400
				const originalHeight = 600

				const scaleX = containerWidth / originalWidth
				const scaleY = containerHeight / originalHeight
				const scale = Math.min(scaleX, scaleY)

				scaledX = position.x * scale
				scaledY = position.y * scale
				scaledSize = size * scale
			}

			// 确保二维码不会超出容器边界
			const padding = 10
			const maxX = containerWidth - scaledSize - padding
			const maxY = containerHeight - scaledSize - padding

			scaledX = Math.max(padding, Math.min(scaledX, maxX))
			scaledY = Math.max(padding, Math.min(scaledY, maxY))

			// 确保最小尺寸
			scaledSize = Math.max(60, scaledSize)

			const style = {
				left: `${scaledX}px`,
				top: `${scaledY}px`,
				width: `${scaledSize}px`,
				height: `${scaledSize}px`
			}

			console.log('🎨 计算出的二维码样式:', style)
			console.log('📐 容器尺寸:', { containerWidth, containerHeight })
			return style
		},

		// 检查登录状态
		checkLoginStatus() {
			this.isLoggedIn = userStore.isLoggedIn()
			console.log('checkLoginStatus - isLoggedIn:', this.isLoggedIn)
			if (this.isLoggedIn) {
				this.userInfo = userStore.getCurrentUser()
				console.log('当前用户信息:', this.userInfo)
				console.log('用户ID:', this.userInfo?.id)
				console.log('用户ID类型:', typeof this.userInfo?.id)
			} else {
				this.userInfo = {}
				console.log('用户未登录，清空用户信息')
			}
		},

		// 下拉刷新
		async onRefresh() {
			this.refreshing = true
			try {
				await this.refreshUserInfo()
				uni.showToast({
					title: '刷新成功',
					icon: 'success',
					duration: 1000
				})
			} catch (error) {
				uni.showToast({
					title: '刷新失败',
					icon: 'none'
				})
			} finally {
				this.refreshing = false
			}
		},

		// 刷新用户信息
		async refreshUserInfo() {
			try {
				// 先从本地获取用户信息
				const currentUser = userStore.getCurrentUser()
				if (currentUser) {
					this.userInfo = currentUser
					console.log('本地用户信息:', this.userInfo)
				}

				// 调用后端API获取最新信息
				const response = await memberAPI.getCurrentUser()
				if (response && response.success) {
					this.userInfo = response.data
					userStore.setCurrentUser(response.data)
					console.log('从后端获取的最新用户信息:', this.userInfo)
				}
			} catch (error) {
				console.error('刷新用户信息失败:', error)
				// 如果API调用失败，继续使用本地数据
				const currentUser = userStore.getCurrentUser()
				if (currentUser) {
					this.userInfo = currentUser
				}
				throw error
			}
		},

		// 获取会员状态文本
		getMemberStatusText() {
			if (!this.userInfo.status) return '普通用户'
			switch (this.userInfo.status) {
				case 'active': return '活跃会员'
				case 'vip': return 'VIP会员'
				case 'partner': return '合伙人'
				default: return '普通用户'
			}
		},

		// 点击头像
		handleAvatarClick() {
			if (!this.isLoggedIn) {
				console.log('用户未登录，显示登录弹窗');
				this.showLoginModal();
			} else {
				// 直接选择图片（优先从相册选择，也支持拍照）
				this.chooseImage(['album', 'camera']);
			}
		},

		// 选择图片
		chooseImage(sourceType) {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'], // 压缩图片
				sourceType: sourceType, // 直接传入数组，支持多种来源
				success: (res) => {
					const tempFilePath = res.tempFilePaths[0];
					console.log('选择的图片路径:', tempFilePath);

					// 直接上传，不显示确认弹窗
					this.uploadAvatar(tempFilePath);
				},
				fail: (err) => {
					console.error('选择图片失败:', err);
					uni.showToast({
						title: '选择图片失败',
						icon: 'none'
					});
				}
			});
		},

		// 上传头像
		async uploadAvatar(filePath) {
			console.log('开始上传头像:', filePath);
			// 使用主服务器端口3030
			const uploadUrl = this.API_BASE_URL + '/api/members/upload-avatar';
			console.log('API地址:', uploadUrl);
			console.log('用户Token:', userStore.getToken());

			uni.showLoading({
				title: '上传中...'
			});

			try {
				// 使用uni.uploadFile上传图片
				const uploadResult = await new Promise((resolve, reject) => {
					uni.uploadFile({
						url: uploadUrl,
						filePath: filePath,
						name: 'avatar',
						header: {
							'Authorization': 'Bearer ' + userStore.getToken()
						},
						success: (res) => {
							console.log('上传响应完整信息:', res);
							console.log('响应状态码:', res.statusCode);
							console.log('响应数据:', res.data);

							if (res.statusCode === 200) {
								try {
									const data = JSON.parse(res.data);
									console.log('解析后的数据:', data);
									if (data.success) {
										resolve(data);
									} else {
										reject(new Error(data.message || '上传失败'));
									}
								} catch (e) {
									console.error('解析响应失败:', e);
									reject(new Error('解析响应失败: ' + e.message));
								}
							} else {
								reject(new Error(`服务器错误: ${res.statusCode}`));
							}
						},
						fail: (err) => {
							console.error('上传请求失败:', err);
							console.error('错误详情:', JSON.stringify(err));
							reject(new Error(`网络请求失败: ${err.errMsg || '未知错误'}`));
						}
					});
				});

				// 上传成功，更新本地用户信息
				console.log('完整上传结果:', JSON.stringify(uploadResult, null, 2));

				// 检查不同的响应格式
				let avatarUrl = null;
				if (uploadResult.data && uploadResult.data.avatarUrl) {
					avatarUrl = uploadResult.data.avatarUrl;
				} else if (uploadResult.avatarUrl) {
					avatarUrl = uploadResult.avatarUrl;
				} else if (uploadResult.data && uploadResult.data.avatar) {
					avatarUrl = uploadResult.data.avatar;
				}

				console.log('提取的头像URL:', avatarUrl);

				if (avatarUrl) {
					// 添加时间戳避免缓存问题
					const avatarUrlWithTimestamp = avatarUrl + '?t=' + Date.now();
					console.log('最终头像URL:', avatarUrlWithTimestamp);

					this.userInfo.avatar = avatarUrlWithTimestamp;
					userStore.setCurrentUser(this.userInfo);

					uni.showToast({
						title: '头像更换成功',
						icon: 'success'
					});

					// 强制刷新页面数据
					this.$forceUpdate();
				} else {
					console.error('未找到头像URL，响应结构:', uploadResult);
					uni.showToast({
						title: '头像URL获取失败',
						icon: 'none'
					});
				}

			} catch (error) {
				console.error('上传头像失败:', error);
				uni.showToast({
					title: error.message || '上传失败',
					icon: 'none',
					duration: 3000
				});
			} finally {
				uni.hideLoading();
			}
		},

		// 跳转到登录页面
		goToLogin() {
			uni.navigateTo({
				url: '/pages/auth/login'
			})
		},

		// 显示智能登录弹窗
		showLoginModal() {
			this.showLoginModalFlag = true
		},

		// 隐藏智能登录弹窗
		hideLoginModal() {
			this.showLoginModalFlag = false
		},

		// 处理登录成功
		handleLoginSuccess(memberData) {
			console.log('登录成功:', memberData)
			// 刷新页面状态
			this.checkLoginStatus()
			// 可以添加其他登录成功后的处理逻辑
		},

		// 测试登录功能
		testLogin() {
			// 模拟登录测试用户ID=11
			const testUser = {
				id: 11,
				phone: '13800138000',
				name: '测试用户',
				username: '测试用户',
				nickname: '测试用户',
				balance: 150,
				points: 1250, // 给测试用户一些点数
				inviteCount: 0,
				status: 'active'
			};
			const testToken = 'test-token';

			// 使用userStore保存用户信息
			userStore.setToken(testToken);
			userStore.setCurrentUser(testUser);

			// 刷新页面状态
			this.checkLoginStatus();

			uni.showToast({
				title: '测试登录成功',
				icon: 'success'
			});
		},

		// 退出登录
		logout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						userStore.clearCurrentUser()
						this.checkLoginStatus()
						uni.showToast({
							title: '已退出登录',
							icon: 'success'
						})
					}
				}
			})
		},

		upgradeMembership: function() {
			if (!this.isLoggedIn) {
				console.log('用户未登录，显示登录弹窗');
				this.showLoginModal();
				return;
			}

			// 显示海报生成弹窗
			this.showPosterModal = true;
			this.initPosterGeneration();
		},
		async navigateToModule(moduleType) {
			console.log('导航到模块:', moduleType)
			console.log('当前登录状态:', this.isLoggedIn)
			console.log('当前用户信息:', this.userInfo)

			// 检查是否需要登录
			if (!this.isLoggedIn && ['wallet', 'points', 'team', 'packages'].includes(moduleType)) {
				console.log('用户未登录，显示登录弹窗');
				this.showLoginModal();
				return;
			}

			const moduleNames = {
				wallet: '钱包',
				points: '点数',
				packages: '套餐',
				team: '团队',
				'customer-service': '客服',
				settings: '设置',
				promotion: '推广'
			}

			// 特殊处理钱包模块
			if (moduleType === 'wallet') {
				if (this.isLoggedIn) {
					// 确保用户信息已加载
					if (!this.userInfo.id) {
						await this.refreshUserInfo()
					}
					// 清除其他弹窗状态
					this.showPointsModal = false
					this.showPackageModal = false
					this.showSettingsModal = false

					this.showWalletModal = true
					this.errorMessage = '' // 清除错误信息
					await this.loadBalanceHistory()
				} else {
					this.showLoginModal();
				}
				return
			}

			// 特殊处理点数模块
			if (moduleType === 'points') {
				console.log('=== 点击点数模块开始 ===')
				console.log('isLoggedIn:', this.isLoggedIn)
				console.log('showPointsModal 当前值:', this.showPointsModal)

				if (!this.isLoggedIn) {
					console.log('未登录，显示登录弹窗')
					this.showLoginModal()
					return
				}

				console.log('已登录，显示点数弹窗')
				// 清除其他弹窗状态
				this.showWalletModal = false
				this.showPackageModal = false
				this.showSettingsModal = false

				// 先显示弹窗
				this.showPointsModal = true
				console.log('showPointsModal 设置后:', this.showPointsModal)

				// 强制触发视图更新
				this.$forceUpdate()
				console.log('强制更新视图完成')

				// 加载真实的点数记录数据
				await this.loadPointsHistory()
				console.log('点数记录加载完成，当前数据:', this.pointsHistory)

				console.log('=== 点击点数模块结束 ===')
				return
			}

			// 特殊处理套餐模块 - 显示套餐明细记录
			if (moduleType === 'packages') {
				console.log('=== 点击套餐模块开始 ===')
				console.log('isLoggedIn:', this.isLoggedIn)
				console.log('showPackageModal 当前值:', this.showPackageModal)

				if (this.isLoggedIn) {
					console.log('已登录，显示套餐弹窗')
					// 确保用户信息已加载
					if (!this.userInfo.id) {
						await this.refreshUserInfo()
					}
					// 清除其他弹窗状态
					this.showWalletModal = false
					this.showPointsModal = false
					this.showSettingsModal = false

					// 先显示弹窗
					this.showPackageModal = true
					console.log('showPackageModal 设置后:', this.showPackageModal)

					// 强制触发视图更新
					this.$forceUpdate()
					console.log('强制更新视图完成')

					// 加载真实的套餐记录数据
					await this.loadPackageHistory()
				} else {
					console.log('用户未登录，显示登录弹窗');
					this.showLoginModal();
				}
				return
			}

			// 特殊处理设置模块
			if (moduleType === 'settings') {
				if (this.isLoggedIn) {
					// 清除其他弹窗状态
					this.showWalletModal = false
					this.showPointsModal = false
					this.showPackageModal = false

					this.showSettingsModal = true
				} else {
					console.log('用户未登录，显示登录弹窗');
					this.showLoginModal();
				}
				return
			}

			// 特殊处理客服模块
			if (moduleType === 'customer-service') {
				uni.showModal({
					title: '联系客服',
					content: '请选择联系方式',
					showCancel: true,
					cancelText: '在线客服',
					confirmText: '电话客服',
					success: (res) => {
						if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber: '************'
							})
						} else if (res.cancel) {
							uni.showToast({
								title: '在线客服功能开发中',
								icon: 'none'
							})
						}
					}
				})
				return
			}

			uni.showToast({
				title: `${moduleNames[moduleType]}功能开发中`,
				icon: 'none'
			})
		},

		// 导航栏点击处理
		handleNavChange(event) {
			const { index, item } = event
			this.currentNavIndex = index

			// 如果点击的不是当前页面，进行跳转
			if (item.path && item.path !== '/pages/profile/index') {
				if (item.path === '/pages/index/index' ||
					item.path === '/pages/create/index' ||
					item.path === '/pages/chat/index' ||
					item.path === '/pages/history/index') {
					// 使用 switchTab 跳转到 tabBar 页面
					uni.switchTab({
						url: item.path
					})
				} else {
					// 使用 navigateTo 跳转到普通页面
					uni.navigateTo({
						url: item.path
					})
				}
			}
		},

		// 钱包弹窗相关方法
		closeWalletModal() {
			console.log('关闭钱包弹窗')
			this.showWalletModal = false
			this.activeTab = 'balance' // 重置为余额明细标签
		},

		// 关闭点数弹窗
		closePointsModal() {
			console.log('关闭点数弹窗')
			this.showPointsModal = false
		},

		// 加载点数明细
		async loadPointsHistory() {
			if (!this.isLoggedIn) {
				console.log('用户未登录，跳过加载点数明细')
				this.pointsHistory = []
				return
			}

			this.pointsLoading = true
			console.log('开始加载点数明细...')

			try {
				// 调用新的点数明细API获取完整的点数记录
				console.log('开始调用点数明细API...')
				console.log('用户ID:', this.userInfo.id)
				console.log('API_BASE_URL:', this.API_BASE_URL)
				console.log('Token:', uni.getStorageSync('token'))

				const response = await uni.request({
					url: `${this.API_BASE_URL}/api/members/${this.userInfo.id}/points-records`,
					method: 'GET',
					data: {
						page: 1,
						pageSize: 50 // 获取最近50条记录
					},
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token')}`
					}
				})

				console.log('点数明细API完整响应:', response)
				console.log('响应状态码:', response.statusCode)
				console.log('响应数据:', response.data)

				if (response.statusCode === 200 && response.data && response.data.code === 200) {
					console.log('API调用成功，开始处理数据...')

					// 直接使用API返回的点数记录
					const apiData = response.data.data
					const records = apiData.records || []

					console.log('获取到点数记录数量:', records.length)
					console.log('汇总信息:', apiData.summary)

					// 转换为前端需要的格式
					this.pointsHistory = records.map(record => ({
						id: record.id,
						description: record.description,
						amount: record.amount,
						time: this.formatDateTime(new Date(record.time)),
						balance: record.balance,
						type: record.type,
						appTitle: record.metadata?.agentTitle || record.metadata?.packageTitle || '',
						relatedId: record.relatedId,
						relatedType: record.relatedType
					}))

					console.log('点数明细设置完成，最终数据:', this.pointsHistory)
				} else {
					// API调用失败或返回错误状态码
					console.log('API调用失败，状态码:', response.statusCode)
					console.log('错误响应:', response)

					// 显示空数据，不再使用示例数据
					this.pointsHistory = []
					console.log('API调用失败，显示空数据')
				}
			} catch (error) {
				console.error('加载点数明细失败:', error)
				console.error('错误详情:', error.message || error)

				// 显示空数据
				this.pointsHistory = []
				console.log('API调用异常，显示空数据')
			} finally {
				this.pointsLoading = false
				console.log('点数明细加载完成，最终数据条数:', this.pointsHistory.length)
			}
		},

		// 关闭套餐弹窗
		closePackageModal() {
			this.showPackageModal = false
		},

		// 切换标签页
		switchTab(tab) {
			this.activeTab = tab
			this.errorMessage = '' // 清除错误信息
			if (tab === 'withdraw' && this.withdrawHistory.length === 0) {
				this.loadWithdrawHistory()
			}
		},

		// 计算手续费
		calculateFee() {
			if (!this.partnerConfig || !this.userInfo.balance) return 0
			return (this.userInfo.balance * this.partnerConfig.withdrawalFee / 100).toFixed(2)
		},

		// 计算实际到账金额
		calculateActualAmount() {
			if (!this.userInfo.balance) return 0
			const fee = this.calculateFee()
			return (this.userInfo.balance - fee).toFixed(2)
		},

		// 检查是否可以提交
		canSubmit() {
			if (!this.partnerConfig) return false

			// 微信提现无需输入信息
			if (this.partnerConfig.withdrawalChannel === 'merchant') {
				return true
			}

			// 支付宝提现需要账号和姓名
			if (this.partnerConfig.withdrawalChannel === 'alipay') {
				return this.withdrawForm.account.trim() && this.withdrawForm.name.trim()
			}

			return false
		},

		// 取消提现
		cancelWithdraw() {
			this.activeTab = 'balance'
			this.withdrawForm = {
				account: '',
				name: ''
			}
		},

		// 在弹窗中提交提现
		async submitWithdrawInModal() {
			// 检查最低提现金额
			if (this.partnerConfig.minWithdrawal && this.userInfo.balance < this.partnerConfig.minWithdrawal) {
				this.errorMessage = `最低提现金额为¥${this.partnerConfig.minWithdrawal}`
				return
			}

			if (!this.canSubmit()) {
				this.errorMessage = '请填写完整信息'
				return
			}

			// 清除错误信息
			this.errorMessage = ''

			const withdrawType = this.partnerConfig.withdrawalChannel === 'merchant' ? 'wechat' : 'alipay'
			const accountInfo = {
				account: withdrawType === 'wechat' ? 'auto' : this.withdrawForm.account,
				name: withdrawType === 'wechat' ? (this.userInfo.nickname || this.userInfo.username || this.userInfo.phone || '微信用户') : this.withdrawForm.name
			}



			await this.submitWithdrawRequest(withdrawType, accountInfo, this.userInfo.balance)
		},

		// 加载余额明细
		async loadBalanceHistory() {
			console.log('loadBalanceHistory - userInfo:', this.userInfo)
			console.log('loadBalanceHistory - userInfo.id:', this.userInfo.id)
			if (!this.userInfo.id) {
				console.log('用户ID不存在，无法加载余额明细')
				return
			}

			this.balanceLoading = true
			try {
				const token = uni.getStorageSync('token')
				const url = `${this.API_BASE_URL}/api/members/${this.userInfo.id}/balance-records`
				console.log('发送余额记录请求:', url)
				console.log('使用Token:', token)

				const response = await uni.request({
					url: url,
					method: 'GET',
					header: {
						'Authorization': `Bearer ${token}`
					}
				})

				console.log('余额记录API响应:', response)

				if (response.data.code === 200) {
					const records = response.data.data.records || []
					this.balanceHistory = records.map(record => ({
						type: this.getRecordTypeText(record.type),
						time: this.formatDateTime(record.created_at),
						amount: record.amount,
						description: record.description
					}))
					console.log('余额明细加载成功:', this.balanceHistory)
				} else {
					console.error('获取余额明细失败:', response.data.message)
					this.balanceHistory = []
					uni.showToast({
						title: response.data.message || '获取余额明细失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载余额明细失败:', error)
				this.balanceHistory = []
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				})
			} finally {
				this.balanceLoading = false
			}
		},

		// 加载提现记录
		async loadWithdrawHistory() {
			if (!this.userInfo.id) return

			this.withdrawLoading = true
			try {
				const response = await uni.request({
					url: `${this.API_BASE_URL}/api/members/${this.userInfo.id}/withdraw-records`,
					method: 'GET',
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token')}`
					}
				})

				if (response.data.code === 200) {
					const records = response.data.data.records || []
					this.withdrawHistory = records.map(record => ({
						withdrawType: this.getWithdrawTypeText(record.withdrawType),
						time: this.formatDateTime(record.createdAt),
						amount: record.amount,
						status: this.getWithdrawStatusText(record.status),
						statusClass: this.getWithdrawStatusClass(record.status),
						rejectReason: record.rejectReason || null
					}))
				} else {
					console.error('获取提现记录失败:', response.data.message)
					this.withdrawHistory = []
				}
			} catch (error) {
				console.error('加载提现记录失败:', error)
				this.withdrawHistory = []
			} finally {
				this.withdrawLoading = false
			}
		},



		// 加载套餐记录
		async loadPackageHistory() {
			console.log('loadPackageHistory - userInfo:', this.userInfo)
			if (!this.userInfo.id) {
				console.log('用户ID不存在，无法加载套餐记录')
				this.packageHistory = []
				return
			}

			this.packageLoading = true
			try {
				// 使用订单API获取用户的套餐购买记录
				const response = await orderAPI.getUserOrders(this.userInfo.id, {
					page: 1,
					pageSize: 20,
					status: 'Completed' // 只获取已完成的订单
				})

				console.log('套餐记录API响应:', response)

				if (response && response.data && response.data.orders) {
					this.packageHistory = response.data.orders.map(order => {
						// 计算套餐状态和剩余天数
						const startDate = new Date(order.completedTime || order.paymentTime)
						// 从套餐信息中获取有效期，如果没有则默认30天
						const validityDays = order.agentPackage ? (order.agentPackage.validityDays || 30) : 30
						const endDate = new Date(startDate.getTime() + (validityDays * 24 * 60 * 60 * 1000))
						const now = new Date()

						// 计算剩余天数（精确到天）
						const timeDiff = endDate.getTime() - now.getTime()
						const remainingDays = Math.max(0, Math.ceil(timeDiff / (24 * 60 * 60 * 1000)))
						const status = remainingDays > 0 ? '使用中' : '已过期'

						// 如果套餐已过期，点数清零
						const totalPoints = remainingDays > 0 ?
							(order.agentPackage ? order.agentPackage.totalQuota || 1000 : 1000) : 0
						const dailyLimit = remainingDays > 0 ?
							(order.agentPackage ? order.agentPackage.dailyMaxConsumption || 50 : 50) : 0

						return {
							packageName: order.itemName,
							startTime: this.formatDateTime(startDate),
							endTime: this.formatDateTime(endDate),
							totalPoints: totalPoints,
							dailyLimit: dailyLimit,
							status: status,
							remainingDays: remainingDays,
							orderNo: order.orderNo,
							amount: order.amount
						}
					})
					console.log('套餐记录加载成功:', this.packageHistory)
				} else {
					// 如果没有订单数据，显示示例数据
					this.packageHistory = [
						{
							packageName: 'VIP套餐',
							startTime: this.formatDateTime(new Date()),
							endTime: this.formatDateTime(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
							totalPoints: 1000,
							dailyLimit: 50,
							status: '使用中',
							remainingDays: 30,
							orderNo: 'DEMO001',
							amount: 99.00
						},
						{
							packageName: '基础套餐',
							startTime: this.formatDateTime(new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)),
							endTime: this.formatDateTime(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)),
							totalPoints: 0, // 已过期，点数清零
							dailyLimit: 0,  // 已过期，限制清零
							status: '已过期',
							remainingDays: 0,
							orderNo: 'DEMO002',
							amount: 49.00
						},
						{
							packageName: '专业套餐',
							startTime: this.formatDateTime(new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)),
							endTime: this.formatDateTime(new Date(Date.now() + 15 * 24 * 60 * 60 * 1000)),
							totalPoints: 2000,
							dailyLimit: 100,
							status: '使用中',
							remainingDays: 15,
							orderNo: 'DEMO003',
							amount: 199.00
						}
					]
					console.log('使用默认套餐记录数据')
				}
			} catch (error) {
				console.error('加载套餐记录失败:', error)
				// 出错时显示默认数据
				this.packageHistory = [
					{
						packageName: 'VIP套餐',
						startTime: this.formatDateTime(new Date()),
						endTime: this.formatDateTime(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
						totalPoints: 1000,
						dailyLimit: 50,
						status: '使用中',
						remainingDays: 30,
						orderNo: 'DEMO001',
						amount: 99.00
					},
					{
						packageName: '基础套餐',
						startTime: this.formatDateTime(new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)),
						endTime: this.formatDateTime(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)),
						totalPoints: 500,
						dailyLimit: 20,
						status: '已过期',
						remainingDays: 0,
						orderNo: 'DEMO002',
						amount: 49.00
					}
				]
				console.log('使用默认套餐记录数据')
			} finally {
				this.packageLoading = false
			}
		},

		// 获取记录类型文本
		getRecordTypeText(type) {
			const typeMap = {
				'commission': '团队充值分成',
				'withdraw': '余额提现',
				'withdraw_reject': '提现驳回退款',
				'withdraw_fee': '提现手续费',
				'manual_adjust': '管理员调整',
				'purchase': '购买套餐',
				'redeem': '兑换码'
			}
			return typeMap[type] || type
		},

		// 获取任务描述
		getTaskDescription(task) {
			const typeMap = {
				'chat': '智能对话',
				'image': '图片生成',
				'text': '文本处理',
				'code': '代码生成',
				'analysis': '数据分析',
				'translation': '翻译服务'
			}

			let description = typeMap[task.taskType] || task.taskTypeDetail || task.taskType || '智能服务'

			// 如果有任务来源，添加到描述中
			if (task.taskSource && task.taskSource !== task.taskType) {
				description += ` (${task.taskSource})`
			}

			return description
		},

		// 获取任务类型显示名称
		getTaskTypeDisplay(taskType) {
			const typeMap = {
				'chat': '智能对话',
				'image': '图片生成',
				'text': '文本处理',
				'code': '代码生成',
				'analysis': '数据分析',
				'translation': '翻译服务'
			}
			return typeMap[taskType] || '智能服务'
		},

		// 获取当前点数
		getCurrentPoints() {
			if (!this.userInfo) return 0

			// 优先使用points字段，如果没有则使用totalQuota - usedQuota
			if (this.userInfo.points !== undefined) {
				return this.userInfo.points || 0
			}

			// 备用方案：使用totalQuota - usedQuota
			const total = this.userInfo.totalQuota || 0
			const used = this.userInfo.usedQuota || 0
			return Math.max(0, total - used)
		},

		// 获取点数记录类型文本
		getPointsTypeText(type) {
			const typeMap = {
				'purchase': '购买套餐',
				'consume': '消费扣除',
				'gift': '赠送获得',
				'refund': '退款返还',
				'manual_adjust': '管理员调整'
			}
			return typeMap[type] || type
		},

		// 获取点数记录类型图标
		getPointsTypeIcon(type) {
			console.log('获取图标，类型:', type)
			const iconMap = {
				'register_gift': '🎁',      // 注册赠送
				'invite_gift': '👥',        // 邀请赠送
				'package_purchase': '📦',   // 购买套餐
				'package_redeem': '🎫',     // 兑换套餐
				'app_usage': '🤖',          // 使用应用
				'purchase': '💰',           // 购买
				'consume': '💸',            // 消费
				'gift': '🎁',               // 赠送
				'refund': '↩️',             // 退款
				'manual_adjust': '⚙️'       // 管理员调整
			}
			const icon = iconMap[type] || '📝'
			console.log('返回图标:', icon)
			return icon
		},

		// 获取提现方式文本
		getWithdrawTypeText(type) {
			const typeMap = {
				'alipay': '支付宝',
				'bank_card': '银行卡',
				'wechat': '微信'
			}
			return typeMap[type] || type
		},

		// 获取提现状态文本
		getWithdrawStatusText(status) {
			const statusMap = {
				'pending': '待审核',
				'processing': '处理中',
				'completed': '已完成',
				'rejected': '已驳回'
			}
			return statusMap[status] || status
		},

		// 获取提现状态样式类
		getWithdrawStatusClass(status) {
			const classMap = {
				'pending': 'status-pending',
				'processing': 'status-processing',
				'completed': 'status-completed',
				'rejected': 'status-rejected'
			}
			return classMap[status] || 'status-default'
		},

		// 获取点数描述
		getPointsDescription(type, originalDescription) {
			const typeMap = {
				'register_gift': '注册赠送',
				'invite_gift': '邀请赠送',
				'package_purchase': '购买套餐',
				'package_redeem': '兑换套餐',
				'app_usage': originalDescription || '应用使用',
				'withdrawal': '提现扣除',
				'commission': '推广佣金'
			}
			return typeMap[type] || originalDescription || '点数变动'
		},

		// 格式化日期时间
		formatDateTime(dateStr) {
			if (!dateStr) return ''
			const date = new Date(dateStr)
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			const hours = String(date.getHours()).padStart(2, '0')
			const minutes = String(date.getMinutes()).padStart(2, '0')
			return `${year}-${month}-${day} ${hours}:${minutes}`
		},

		// 加载合伙人配置
		async loadPartnerConfig() {
			console.log('开始加载合伙人配置...')
			// 如果没有token，使用默认配置
			const token = uni.getStorageSync('token')
			console.log('当前token:', token ? '存在' : '不存在')
			if (!token) {
				console.log('未登录，使用默认合伙人配置')
				this.partnerConfig = {
					minWithdrawal: 10,
					withdrawalChannel: 'alipay',
					withdrawalFee: 0
				}
				console.log('设置默认配置:', this.partnerConfig)
				return
			}

			try {
				const response = await uni.request({
					url: `${this.API_BASE_URL}/api/config/type/referral`,
					method: 'GET',
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token') || ''}`,
						'Content-Type': 'application/json'
					}
				})

				console.log('合伙人配置API响应:', response.data)

				// 处理不同的响应格式
				let configArray = []
				if (response.data.code === 200 && response.data.data) {
					configArray = response.data.data
				} else if (response.data.success && response.data.data) {
					configArray = response.data.data
				} else if (response.data && Array.isArray(response.data)) {
					configArray = response.data
				}

				if (configArray.length > 0) {
					// 转换配置数据格式
					const configData = {}
					configArray.forEach(item => {
						configData[item.key] = item.value
					})

					this.partnerConfig = {
						minWithdrawal: parseInt(configData.min_withdrawal || '10'),
						withdrawalChannel: configData.withdrawal_channel || 'alipay',
						withdrawalFee: parseFloat(configData.withdrawal_fee || '0')
					}
					console.log('合伙人配置加载成功:', this.partnerConfig)
				} else {
					console.warn('合伙人配置加载失败，使用默认配置')
					this.partnerConfig = {
						minWithdrawal: 10,
						withdrawalChannel: 'alipay',
						withdrawalFee: 0
					}
				}
			} catch (error) {
				console.error('加载合伙人配置失败:', error)
				console.error('错误详情:', error.errMsg || error.message || error)
				// 使用默认配置
				this.partnerConfig = {
					minWithdrawal: 10,
					withdrawalChannel: 'alipay',
					withdrawalFee: 0
				}
			}
		},

		// 余额提现
		async handleWithdraw() {
			console.log('handleWithdraw 被调用')
			console.log('userInfo.balance:', this.userInfo.balance)
			console.log('partnerConfig:', this.partnerConfig)

			// 检查余额
			if (!this.userInfo.balance || this.userInfo.balance <= 0) {
				// 在弹窗内显示错误信息
				this.errorMessage = '余额不足，无法提现'
				console.log('设置错误信息:', this.errorMessage)
				return
			}

			// 如果合伙人配置未加载，尝试重新加载
			if (!this.partnerConfig) {
				console.log('合伙人配置未加载，尝试重新加载')
				await this.loadPartnerConfig()
				console.log('重新加载后的partnerConfig:', this.partnerConfig)
			}

			// 检查合伙人配置
			if (!this.partnerConfig || !this.partnerConfig.withdrawalChannel) {
				this.errorMessage = '提现配置未加载，请稍后重试'
				console.log('设置错误信息:', this.errorMessage)
				return
			}

			// 检查最低提现金额
			if (this.partnerConfig.minWithdrawal && this.userInfo.balance < this.partnerConfig.minWithdrawal) {
				this.errorMessage = `最低提现金额为¥${this.partnerConfig.minWithdrawal}`
				console.log('设置错误信息:', this.errorMessage)
				return
			}

			// 清除错误信息
			this.errorMessage = ''
			// 切换到提现表单标签页
			this.activeTab = 'withdrawForm'
			console.log('切换到提现表单，activeTab:', this.activeTab)
			// 清空表单数据
			this.withdrawForm = {
				account: '',
				name: ''
			}
		},

		// 微信提现处理（无需输入账号，直接全额提现）
		async processWechatWithdraw() {
			const fullAmount = this.userInfo.balance

			// 检查最低提现金额
			if (this.partnerConfig.minWithdrawal && fullAmount < this.partnerConfig.minWithdrawal) {
				this.errorMessage = `最低提现金额为¥${this.partnerConfig.minWithdrawal}`
				return
			}

			// 清除错误信息
			this.errorMessage = ''

			const fee = this.partnerConfig ? (fullAmount * this.partnerConfig.withdrawalFee / 100) : 0
			const actualAmount = fullAmount - fee

			const confirmText = `确认提现到微信零钱？\n提现金额：¥${fullAmount}\n手续费：¥${fee.toFixed(2)}\n实际到账：¥${actualAmount.toFixed(2)}`

			uni.showModal({
				title: '微信提现确认',
				content: confirmText,
				success: (res) => {
					if (res.confirm) {
						this.submitWithdrawRequest('wechat', {
							account: 'auto', // 微信自动到账，无需账号
							name: this.userInfo.nickname || this.userInfo.username || this.userInfo.phone || '微信用户'
						}, fullAmount)
					}
				}
			})
		},



		// 显示提现表单（支付宝全额提现）
		showWithdrawForm(withdrawType) {
			const typeNames = {
				'alipay': '支付宝',
				'bank_card': '银行卡',
				'wechat': '微信'
			}

			// 全额提现
			const fullAmount = this.userInfo.balance

			// 检查最低提现金额
			if (this.partnerConfig.minWithdrawal && fullAmount < this.partnerConfig.minWithdrawal) {
				this.errorMessage = `最低提现金额为¥${this.partnerConfig.minWithdrawal}`
				return
			}

			// 清除错误信息
			this.errorMessage = ''

			const feeRate = this.partnerConfig ? this.partnerConfig.withdrawalFee : 0
			const fee = fullAmount * feeRate / 100
			const actualAmount = fullAmount - fee

			// 构建确认内容
			let content = `将提现全部余额到${typeNames[withdrawType]}\n`
			content += `提现金额：¥${fullAmount}\n`
			if (feeRate > 0) {
				content += `手续费：¥${fee.toFixed(2)} (${feeRate}%)\n`
				content += `实际到账：¥${actualAmount.toFixed(2)}`
			} else {
				content += `实际到账：¥${actualAmount.toFixed(2)}`
			}

			// 确认全额提现
			uni.showModal({
				title: `${typeNames[withdrawType]}提现确认`,
				content: content,
				success: (res) => {
					if (res.confirm) {
						this.showAccountForm(withdrawType, fullAmount)
					}
				}
			})
		},

		// 显示账户信息表单（支付宝）
		showAccountForm(withdrawType, amount) {
			// 只处理支付宝提现，收集账号
			uni.showModal({
				title: '支付宝账号',
				content: '请输入支付宝账号（手机号或邮箱）',
				editable: true,
				placeholderText: '支付宝账号',
				success: (res) => {
					if (res.confirm && res.content) {
						// 收集真实姓名
						uni.showModal({
							title: '真实姓名',
							content: '请输入支付宝账号对应的真实姓名',
							editable: true,
							placeholderText: '真实姓名',
							success: (nameRes) => {
								if (nameRes.confirm && nameRes.content) {
									this.submitWithdrawRequest(withdrawType, {
										account: res.content,
										name: nameRes.content
									}, amount)
								}
							}
						})
					}
				}
			})
		},

		// 提交提现申请
		async submitWithdrawRequest(withdrawType, accountInfo, amount) {
			uni.showLoading({
				title: '提交中...'
			})

			try {
				const response = await uni.request({
					url: `${this.API_BASE_URL}/api/members/${this.userInfo.id}/withdraw`,
					method: 'POST',
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token')}`,
						'Content-Type': 'application/json'
					},
					data: {
						amount,
						withdrawType,
						accountInfo
					}
				})

				uni.hideLoading()

				if (response.data.code === 200) {
					uni.showToast({
						title: '提现申请提交成功',
						icon: 'success'
					})
					// 刷新用户信息和记录
					await this.refreshUserInfo()
					this.loadWithdrawHistory()
					this.loadBalanceHistory()
					// 切换到提现记录标签
					this.activeTab = 'withdraw'
				} else {
					uni.showToast({
						title: response.data.message || '提现申请失败',
						icon: 'none'
					})
				}
			} catch (error) {
				uni.hideLoading()
				console.error('提现申请失败:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			}
		},

		// 设置弹窗相关方法
		closeSettingsModal() {
			this.showSettingsModal = false
		},

		handlePersonalSettings() {
			this.closeSettingsModal()
			uni.showToast({
				title: '个人设置功能开发中',
				icon: 'none'
			})
		},

		handleAccountSecurity() {
			this.closeSettingsModal()
			uni.showToast({
				title: '账户安全功能开发中',
				icon: 'none'
			})
		},

		handleLogout() {
			this.closeSettingsModal()
			this.logout()
		},



		// 初始化海报生成
		async initPosterGeneration() {
			try {
				console.log('🎯 开始初始化海报生成流程')
				this.posterGenerating = true

				// 检查缓存
				const userId = this.userInfo.id
				if (userId && this.checkQRCodeCache(userId)) {
					console.log('📋 使用缓存的二维码信息')
					const cached = this.qrCodeCache[userId]
					this.inviteCode = cached.inviteCode
					this.inviteUrl = cached.inviteUrl
					this.qrCodeUrl = cached.qrCodeUrl
					console.log('🎉 海报生成流程完成（使用缓存）！')
					return
				}

				// 获取邀请码和链接
				await this.getInviteInfo()
				console.log('📋 邀请信息获取完成:', { inviteCode: this.inviteCode, inviteUrl: this.inviteUrl })

				// 生成二维码
				const qrCodePath = await this.generateQRCodeFromServer()
				// 如果是完整的URL（降级方案），直接使用；否则拼接完整URL
				if (qrCodePath.startsWith('http')) {
					this.qrCodeUrl = qrCodePath
				} else {
					this.qrCodeUrl = this.getFullImageUrl(qrCodePath)
				}
				console.log('🔗 二维码生成完成:', this.qrCodeUrl)

				// 缓存二维码信息
				if (userId) {
					this.cacheQRCodeInfo(userId, this.qrCodeUrl, this.inviteCode, this.inviteUrl)
				}

				console.log('🎉 海报生成流程全部完成！')

			} catch (error) {
				console.error('❌ 初始化海报失败:', error)

				// 即使出错也尝试生成一个基本的二维码
				try {
					if (!this.qrCodeUrl && this.inviteUrl) {
						console.log('🔄 尝试生成降级二维码...')
						this.qrCodeUrl = await this.generateQRCodeFallback()
						console.log('✅ 降级二维码生成成功:', this.qrCodeUrl)
					}
				} catch (fallbackError) {
					console.error('❌ 降级二维码也失败:', fallbackError)
				}

				uni.showToast({
					title: '生成海报失败: ' + error.message,
					icon: 'none'
				})
			} finally {
				this.posterGenerating = false
			}
		},

		// 加载后台海报配置
		async loadPosterConfig() {
			try {
				console.log('📋 请求后台海报配置...')
				const response = await uni.request({
					url: `${this.API_BASE_URL}/api/config/referral`,
					method: 'GET'
				})

				console.log('📋 后台配置响应:', response)

				if (response.statusCode === 200 && response.data.success) {
					const config = response.data.data
					console.log('📋 获取到的配置:', config)

					// 更新海报配置
					if (config.poster_background_image) {
						this.posterConfig.backgroundImage = config.poster_background_image
						this.posterConfig.posterbackgroundimage = config.poster_background_image
						console.log('🖼️ 设置背景图片:', config.poster_background_image)
						console.log('🖼️ posterConfig更新后:', this.posterConfig)
					} else {
						console.log('⚠️ 后台配置中没有poster_background_image字段')
						console.log('📋 完整配置:', config)
					}
					if (config.poster_qr_code_size) {
						this.posterConfig.qrCodeSize = parseInt(config.poster_qr_code_size)
						console.log('📏 设置二维码尺寸:', this.posterConfig.qrCodeSize)
					}
					if (config.poster_qr_code_position_x !== undefined && config.poster_qr_code_position_y !== undefined) {
						this.posterConfig.qrCodePosition.x = parseInt(config.poster_qr_code_position_x)
						this.posterConfig.qrCodePosition.y = parseInt(config.poster_qr_code_position_y)
						console.log('📍 设置二维码位置:', this.posterConfig.qrCodePosition)
					}

					// 更新分享配置
					if (config.share_title) {
						this.shareConfig.title = config.share_title
						console.log('📝 设置分享标题:', config.share_title)
					}
					if (config.share_description) {
						this.shareConfig.description = config.share_description
						console.log('📄 设置分享描述:', config.share_description)
					}

					console.log('✅ 海报配置加载完成:', {
						posterConfig: this.posterConfig,
						shareConfig: this.shareConfig
					})
				} else {
					console.warn('⚠️ 后台配置响应异常:', response.data)
				}
			} catch (error) {
				console.error('❌ 加载海报配置失败:', error)
				// 使用默认配置
			}
		},

		// 生成邀请码
		async generateInviteCode() {
			try {
				const token = userStore.getToken()
				const response = await uni.request({
					url: `${API_BASE_URL}/api/config/generate-invite-code`,
					method: 'POST',
					header: {
						'Authorization': `Bearer ${token}`,
						'Content-Type': 'application/json'
					}
				})

				if (response.statusCode === 200 && response.data.success) {
					this.inviteCode = response.data.data.inviteCode
					this.inviteUrl = response.data.data.inviteUrl
				} else {
					throw new Error(response.data.message || '生成邀请码失败')
				}
			} catch (error) {
				console.error('生成邀请码失败:', error)
				throw error
			}
		},

		// 获取邀请信息
		async getInviteInfo() {
			try {
				// 首先尝试调用后端API生成邀请码
				const token = userStore.getToken()
				if (token) {
					const response = await uni.request({
						url: `${this.API_BASE_URL}/api/config/generate-invite-code`,
						method: 'POST',
						header: {
							'Authorization': `Bearer ${token}`,
							'Content-Type': 'application/json'
						}
					})

					if (response.statusCode === 200 && response.data.success) {
						this.inviteCode = response.data.data.inviteCode
						this.inviteUrl = response.data.data.inviteUrl
						console.log('✅ 从后端获取邀请信息成功:', { inviteCode: this.inviteCode, inviteUrl: this.inviteUrl })
						return
					}
				}
			} catch (error) {
				console.warn('⚠️ 后端获取邀请信息失败，使用本地生成:', error)
			}

			// 降级方案：本地生成邀请码和链接
			const userId = this.userInfo.id || 'user123'
			this.inviteCode = `INV${userId}${Date.now().toString().slice(-6)}`

			// 生成正确的邀请链接
			const baseUrl = window.location.origin
			this.inviteUrl = `${baseUrl}/#/pages/auth/register?inviteCode=${this.inviteCode}&inviter=${userId}`
			console.log('📋 本地生成邀请信息:', { inviteCode: this.inviteCode, inviteUrl: this.inviteUrl })
		},

		// 调用后端生成二维码图片文件
		async generateQRCodeFromServer() {
			try {
				console.log('📡 调用后端API生成二维码...')

				const response = await uni.request({
					url: `${this.API_BASE_URL}/api/config/generate-qrcode`,
					method: 'POST',
					header: {
						'Authorization': `Bearer ${userStore.getToken()}`,
						'Content-Type': 'application/json'
					},
					data: {
						inviteCode: this.inviteCode,
						size: this.posterConfig.qrCodeSize || 200
					}
				})

				console.log('📡 后端响应:', response)

				if (response.statusCode === 200 && response.data.success) {
					const qrCodePath = response.data.data.qrCodePath
					console.log('✅ 二维码生成成功，路径:', qrCodePath)
					return qrCodePath
				} else {
					throw new Error(response.data.message || '生成二维码失败')
				}
			} catch (error) {
				console.error('❌ 调用后端生成二维码失败:', error)
				// 降级方案：使用在线API
				return this.generateQRCodeFallback()
			}
		},

		// 降级方案：使用在线API生成二维码
		async generateQRCodeFallback() {
			console.log('🔄 使用降级方案生成二维码')
			const inviteUrl = `${window.location.origin}/#/pages/auth/register?inviteCode=${this.inviteCode}`
			const size = this.posterConfig.qrCodeSize || 200
			return `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(inviteUrl)}&format=png&ecc=M`
		},

		// 背景图片加载成功
		onBackgroundImageLoad(e) {
			console.log('✅ 背景图片加载成功:', e)
			console.log('🖼️ 背景图片配置:', this.posterConfig)
			console.log('🖼️ 图片尺寸:', e.detail)
			this.backgroundImageError = false

			// 获取图片的真实尺寸并调整容器高度
			if (e && e.detail) {
				const { width: imgWidth, height: imgHeight } = e.detail
				if (imgWidth && imgHeight) {
					console.log('📐 背景图片真实尺寸:', { imgWidth, imgHeight })

					// 更新海报配置中的比例信息
					this.posterConfig.imageAspectRatio = imgWidth / imgHeight

					// 强制重新计算容器样式
					this.$forceUpdate()

					console.log('🔄 已更新图片比例:', this.posterConfig.imageAspectRatio)
				}
			}
		},

		// 背景图片加载失败
		onBackgroundImageError(e) {
			const fullUrl = this.getFullImageUrl(this.posterConfig.backgroundImage || this.posterConfig.posterbackgroundimage)
			console.log('❌ 背景图片加载失败，将显示默认背景:', e)
			console.log('🔍 尝试加载的图片URL:', fullUrl)
			console.log('🔍 posterConfig.backgroundImage:', this.posterConfig.backgroundImage)
			console.log('🔍 posterConfig.posterbackgroundimage:', this.posterConfig.posterbackgroundimage)
			console.log('🔍 完整posterConfig:', this.posterConfig)
			// 背景图片加载失败时，默认背景会自动显示（因为背景图片不会覆盖它）
		},

		// 二维码图片加载错误处理
		onQRCodeImageError(e) {
			console.error('❌ 二维码图片加载失败:', e)
			console.log('🔍 二维码URL:', this.qrCodeUrl)
			uni.showToast({
				title: '二维码加载失败',
				icon: 'none'
			})
		},

		// 二维码图片加载成功处理
		onQRCodeImageLoad(e) {
			console.log('✅ 二维码图片加载成功:', e)
			console.log('🔍 二维码URL:', this.qrCodeUrl)
		},

		// 生成海报
		async generatePoster() {
			console.log('🚀 开始生成海报...')
			this.posterGenerating = true

			try {
				// 先加载后台海报配置
				console.log('📋 加载后台海报配置...')
				await this.loadPosterConfig()
				console.log('✅ 后台配置加载完成')

				// 生成邀请码
				console.log('🎫 生成邀请码...')
				await this.generateInviteCode()
				console.log('✅ 邀请码生成完成:', this.inviteCode)

				// #ifdef H5
				// H5环境使用原生Canvas
				await this.generatePosterH5()
				// #endif

				// #ifndef H5
				// 非H5环境使用uni-app Canvas
				await this.generatePosterUniApp()
				// #endif

				// 海报已经在模板中显示，不需要额外设置
				console.log('🎉 海报生成完成！')

			} catch (error) {
				console.error('❌ 生成海报失败:', error)
				uni.showToast({
					title: '生成海报失败: ' + error.message,
					icon: 'none'
				})
			} finally {
				this.posterGenerating = false
			}
		},

		// H5环境海报生成
		async generatePosterH5() {
			console.log('🌐 H5环境：使用简单方案生成海报')

			// 调用后端API生成二维码图片文件
			console.log('🔲 调用后端生成二维码图片...')
			const qrCodePath = await this.generateQRCodeFromServer()

			// 使用完整URL显示二维码
			this.qrCodeUrl = this.getFullImageUrl(qrCodePath)

			console.log('✅ H5海报生成完成')
			console.log('🔲 二维码图片路径:', qrCodePath)
			console.log('🔲 二维码完整URL:', this.qrCodeUrl)
			console.log('🖼️ 背景图片路径:', this.posterConfig.backgroundImage)
			console.log('🖼️ 完整背景图片URL:', this.getFullImageUrl(this.posterConfig.backgroundImage))
		},

		// 非H5环境海报生成
		async generatePosterUniApp() {
			console.log('📱 非H5环境：使用uni-app Canvas生成海报')
			throw new Error('非H5环境暂不支持')
		},

		// 绘制海报内容
		async drawPosterContent(ctx) {
			try {
				console.log('🎨 开始绘制海报内容...')

				// 先绘制一个简单的背景，确保有内容显示
				console.log('🎨 绘制简单背景')
				ctx.setFillStyle('#667eea')
				ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)

				// 绘制标题
				console.log('📝 开始绘制标题...')
				this.drawTitle(ctx)
				console.log('✅ 标题绘制完成')

				// 绘制描述
				console.log('📄 开始绘制描述...')
				this.drawDescription(ctx)
				console.log('✅ 描述绘制完成')

				// 绘制邀请码
				console.log('🎫 开始绘制邀请码...')
				this.drawInviteCode(ctx)
				console.log('✅ 邀请码绘制完成')

				// 绘制二维码占位符
				console.log('🔲 开始绘制二维码占位符...')
				this.drawQRCodePlaceholder(ctx,
					this.posterConfig.qrCodePosition.x,
					this.posterConfig.qrCodePosition.y,
					this.posterConfig.qrCodeSize)
				console.log('✅ 二维码占位符绘制完成')

				console.log('🎉 海报内容绘制全部完成')

			} catch (error) {
				console.error('❌ 绘制海报内容失败:', error)
				// 即使出错也不抛出异常，让海报能够显示
				console.log('⚠️ 继续导出海报，忽略绘制错误')
			}
		},

		// 绘制背景
		async drawBackground(ctx) {
			console.log('🖼️ 背景图片URL:', this.posterConfig.backgroundImage)

			// 暂时先使用默认背景，确保海报能正常显示
			console.log('🎨 使用默认渐变背景')
			// 使用默认渐变背景
			const gradient = ctx.createLinearGradient(0, 0, 0, this.canvasHeight)
			gradient.addColorStop(0, '#667eea')
			gradient.addColorStop(1, '#764ba2')

			ctx.setFillStyle(gradient)
			ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)

			// 如果有背景图片配置，尝试加载（但不阻塞）
			if (this.posterConfig.backgroundImage) {
				try {
					console.log('📥 尝试下载背景图片...')
					const downloadResult = await Promise.race([
						uni.downloadFile({
							url: this.posterConfig.backgroundImage
						}),
						new Promise((_, reject) =>
							setTimeout(() => reject(new Error('下载超时')), 3000)
						)
					])

					console.log('📥 下载结果:', downloadResult)

					if (downloadResult.statusCode === 200) {
						console.log('✅ 背景图片下载成功，重新绘制')
						// 重新绘制背景图片
						ctx.drawImage(downloadResult.tempFilePath, 0, 0, this.canvasWidth, this.canvasHeight)
						// 重新绘制canvas
						ctx.draw()
					}
				} catch (error) {
					console.warn('⚠️ 背景图片加载失败，使用默认背景:', error.message)
				}
			}
		},

		// 绘制标题
		drawTitle(ctx) {
			ctx.setFillStyle('#ffffff')
			ctx.setFontSize(24)
			ctx.setTextAlign('center')
			ctx.fillText(this.shareConfig.title, this.canvasWidth / 2, 80)
		},

		// 绘制描述
		drawDescription(ctx) {
			ctx.setFillStyle('#ffffff')
			ctx.setFontSize(16)
			ctx.setTextAlign('center')

			// 处理长文本换行
			const maxWidth = this.canvasWidth - 40
			const lines = this.wrapText(ctx, this.shareConfig.description, maxWidth)

			lines.forEach((line, index) => {
				ctx.fillText(line, this.canvasWidth / 2, 120 + index * 25)
			})
		},



		// 绘制二维码
		async drawQRCode(ctx) {
			const qrX = this.posterConfig.qrCodePosition.x
			const qrY = this.posterConfig.qrCodePosition.y
			const qrSize = this.posterConfig.qrCodeSize

			console.log('🔲 二维码位置:', qrX, qrY, '尺寸:', qrSize)
			console.log('🔗 邀请链接:', this.inviteUrl)

			// 先绘制占位符，确保海报能正常显示
			this.drawQRCodePlaceholder(ctx, qrX, qrY, qrSize)

			// 如果有邀请链接，尝试生成真实二维码（异步，不阻塞海报生成）
			if (this.inviteUrl) {
				try {
					// 设置较短的超时时间，避免阻塞
					const qrCodePath = await this.generateQRCodeFromServer()
					const qrCodeUrl = this.getFullImageUrl(qrCodePath)
					console.log('🔲 二维码URL:', qrCodeUrl)

					if (qrCodeUrl) {
						// 下载二维码图片
						const downloadResult = await Promise.race([
							uni.downloadFile({
								url: qrCodeUrl
							}),
							new Promise((_, reject) =>
								setTimeout(() => reject(new Error('下载超时')), 5000)
							)
						])

						console.log('📥 二维码下载结果:', downloadResult)

						if (downloadResult.statusCode === 200) {
							// 重新绘制白色背景
							ctx.setFillStyle('#ffffff')
							ctx.fillRect(qrX - 5, qrY - 5, qrSize + 10, qrSize + 10)

							// 绘制二维码图片
							ctx.drawImage(downloadResult.tempFilePath, qrX, qrY, qrSize, qrSize)
							console.log('✅ 二维码绘制成功')

							// 重新绘制canvas
							ctx.draw()
						} else {
							console.warn('⚠️ 二维码下载失败，状态码:', downloadResult.statusCode)
						}
					}
				} catch (error) {
					console.warn('⚠️ 二维码生成失败，使用占位符:', error.message)
				}
			}
		},

		// 绘制二维码占位符
		drawQRCodePlaceholder(ctx, qrX, qrY, qrSize) {
			// 绘制白色背景
			ctx.setFillStyle('#ffffff')
			ctx.fillRect(qrX - 5, qrY - 5, qrSize + 10, qrSize + 10)

			// 绘制二维码占位符边框
			ctx.setStrokeStyle('#cccccc')
			ctx.setLineWidth(2)
			ctx.strokeRect(qrX, qrY, qrSize, qrSize)

			// 绘制占位符内容
			ctx.setFillStyle('#666666')
			ctx.setFontSize(14)
			ctx.setTextAlign('center')
			ctx.fillText('扫码注册', qrX + qrSize / 2, qrY + qrSize / 2 - 10)

			ctx.setFontSize(12)
			ctx.setFillStyle('#999999')
			ctx.fillText(this.inviteCode || 'INV123456', qrX + qrSize / 2, qrY + qrSize / 2 + 10)
		},

		// 绘制邀请码
		drawInviteCode(ctx) {
			ctx.setFillStyle('#ffffff')
			ctx.setFontSize(14)
			ctx.setTextAlign('center')
			ctx.fillText(`邀请码：${this.inviteCode}`, this.canvasWidth / 2, this.canvasHeight - 50)
		},

		// 文本换行处理
		wrapText(ctx, text, maxWidth) {
			const words = text.split('')
			const lines = []
			let currentLine = ''

			for (let i = 0; i < words.length; i++) {
				const testLine = currentLine + words[i]
				const metrics = ctx.measureText(testLine)
				const testWidth = metrics.width

				if (testWidth > maxWidth && i > 0) {
					lines.push(currentLine)
					currentLine = words[i]
				} else {
					currentLine = testLine
				}
			}
			lines.push(currentLine)
			return lines
		},

		// 关闭海报弹窗
		closePosterModal() {
			this.showPosterModal = false
			this.posterGenerating = false // 重置生成状态
			console.log('🚪 关闭海报弹窗，保留缓存数据')
			// 注意：不清除 qrCodeUrl、inviteCode、inviteUrl，保持缓存
		},

		// 图片加载成功
		onImageLoad(e) {
			console.log('✅ 图片加载成功:', e)
		},

		// 图片加载失败
		onImageError(e) {
			console.error('❌ 图片加载失败:', e)
			console.log('🔍 当前 posterImageUrl:', this.posterImageUrl)
		},



		// 保存海报到相册
		async savePosterToAlbum() {
			if (!this.qrCodeUrl) {
				uni.showToast({
					title: '海报未生成',
					icon: 'none'
				})
				return
			}

			try {
				// #ifdef H5
				// H5环境下，提示用户截图保存
				uni.showModal({
					title: '保存海报',
					content: '请截图保存海报到相册',
					showCancel: false
				})
				// #endif

				// #ifndef H5
				// 非H5环境，提示用户截图保存
				uni.showToast({
					title: '请截图保存',
					icon: 'none'
				})
				// #endif
			} catch (error) {
				console.error('保存海报失败:', error)
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				})
			}
		},

		// 分享海报
		async sharePoster() {
			if (!this.qrCodeUrl) {
				uni.showToast({
					title: '海报未生成',
					icon: 'none'
				})
				return
			}

			try {
				// #ifdef H5
				// H5环境下，使用 Web Share API 或复制链接
				if (navigator.share) {
					// 支持 Web Share API
					await navigator.share({
						title: this.shareConfig.title,
						text: this.shareConfig.description,
						url: window.location.href
					})
				} else {
					// 不支持 Web Share API，复制链接到剪贴板
					await navigator.clipboard.writeText(window.location.href)
					uni.showToast({
						title: '链接已复制到剪贴板',
						icon: 'success'
					})
				}
				// #endif

				// #ifndef H5
				await uni.share({
					provider: 'weixin',
					scene: 'WXSceneSession',
					type: 2,
					imageUrl: this.posterImageUrl,
					title: this.shareConfig.title,
					summary: this.shareConfig.description
				})
				// #endif
			} catch (error) {
				console.error('分享失败:', error)
				uni.showToast({
					title: '分享失败',
					icon: 'none'
				})
			}
		},

		// 复制邀请码
		copyInviteCode() {
			if (!this.inviteCode) {
				uni.showToast({
					title: '邀请码未生成',
					icon: 'none'
				})
				return
			}

			uni.setClipboardData({
				data: this.inviteCode,
				success: () => {
					uni.showToast({
						title: '邀请码已复制',
						icon: 'success'
					})
				}
			})
		},

		// 复制邀请链接
		copyInviteUrl() {
			if (!this.inviteUrl) {
				uni.showToast({
					title: '邀请链接未生成',
					icon: 'none'
				})
				return
			}

			uni.setClipboardData({
				data: this.inviteUrl,
				success: () => {
					uni.showToast({
						title: '邀请链接已复制',
						icon: 'success'
					})
				}
			})
		},

		// 检查二维码缓存是否有效
		checkQRCodeCache(userId) {
			if (!this.qrCodeCache[userId]) {
				return false
			}

			const cached = this.qrCodeCache[userId]
			const now = Date.now()
			const cacheExpiry = 24 * 60 * 60 * 1000 // 24小时缓存

			// 检查缓存是否过期
			if (now - cached.timestamp > cacheExpiry) {
				delete this.qrCodeCache[userId]
				return false
			}

			return true
		},

		// 缓存二维码信息
		cacheQRCodeInfo(userId, qrCodeUrl, inviteCode, inviteUrl) {
			this.qrCodeCache[userId] = {
				qrCodeUrl,
				inviteCode,
				inviteUrl,
				timestamp: Date.now()
			}
			console.log('💾 缓存二维码信息:', this.qrCodeCache[userId])
		},

		// 清除二维码缓存
		clearQRCodeCache(userId = null) {
			if (userId) {
				delete this.qrCodeCache[userId]
				console.log('🗑️ 清除用户二维码缓存:', userId)
			} else {
				this.qrCodeCache = {}
				console.log('🗑️ 清除所有二维码缓存')
			}
		}
	}
}
</script>

<style scoped>
.profile-page {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
	background-size: 400% 400%;
	min-height: 100vh;
	padding: 0;
	position: relative;
	overflow: hidden;
	animation: gradientFlow 15s ease-in-out infinite;
}

.scroll-container {
	height: 100vh;
	width: 100%;
	position: relative;
	z-index: 2;
}

/* 主背景动画层 */
.profile-page::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background:
		radial-gradient(circle at 20% 20%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
		radial-gradient(circle at 80% 80%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
		radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
		radial-gradient(circle at 60% 60%, rgba(255, 180, 120, 0.2) 0%, transparent 50%);
	animation: floatingOrbs 20s ease-in-out infinite;
	z-index: 1;
}

/* 第二层动画效果 */
.profile-page::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background:
		linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%),
		linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
	background-size: 200% 200%;
	animation: shimmerEffect 8s linear infinite;
	z-index: 1;
}

@keyframes gradientShift {
	0%, 100% { opacity: 0.3; }
	50% { opacity: 0.6; }
}

/* 顶部用户信息 */
.header-section {
	padding: 40px 0 0;
	position: relative;
	z-index: 1;
}

/* 用户基本信息 - 第一排 */
.user-basic-info {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	margin: 0 20px 12px 20px;
	flex: 0 0 auto;
	padding: 0;
}

/* 用户文字信息容器 */
.user-text-info {
	display: flex;
	flex-direction: column;
	gap: 5px;
	flex: 1;
	margin-left: 15px;
}

.user-avatar {
	position: relative;
	cursor: pointer;
	width: 60px;
	height: 60px;
	border-radius: 50%;
	overflow: hidden;
}

.avatar-img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	object-position: center;
	transition: all 0.3s ease;
	display: block;
	border: none;
	outline: none;
}

.avatar-border {
	position: absolute;
	top: -3px;
	left: -3px;
	right: -3px;
	bottom: -3px;
	border-radius: 50%;
	background: linear-gradient(45deg, #667eea, #764ba2, #00ff87, #60efff);
	background-size: 400% 400%;
	animation: gradientShift 3s ease infinite;
	z-index: -1;
}

.avatar-edit-hint {
	position: absolute;
	bottom: -2px;
	right: -2px;
	width: 20px;
	height: 20px;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
	transition: all 0.3s ease;
}

.edit-icon {
	font-size: 10px;
	line-height: 1;
}

.user-avatar:hover .avatar-edit-hint {
	transform: scale(1.1);
	background: rgba(255, 255, 255, 1);
}

.user-avatar:active .avatar-img {
	transform: scale(0.95);
}

.user-avatar:active .avatar-edit-hint {
	transform: scale(0.9);
}

.username {
	color: rgba(255, 255, 255, 0.95);
	font-size: 18px;
	font-weight: 700;
	text-align: left;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
	margin-bottom: 2px;
}

.user-uid {
	color: rgba(255, 255, 255, 0.7);
	font-size: 13px;
	font-weight: 500;
	text-align: left;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.login-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 20px;
	padding: 8px 16px;
	font-size: 12px;
	font-weight: 500;
	box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
}

.login-btn:active {
	transform: translateY(2px);
	box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 用户统计信息 - 第二排 */
.user-stats-section {
	margin: 40px 20px 24px 20px;
	padding: 0;
}

.stats-container {
	display: flex;
	align-items: center;
	justify-content: space-around;
	background: rgba(255, 255, 255, 0.12);
	backdrop-filter: blur(20px);
	border-radius: 20px;
	padding: 18px;
	border: 1px solid rgba(255, 255, 255, 0.2);
	box-shadow:
		0 8px 32px rgba(0, 0, 0, 0.1),
		inset 0 1px 0 rgba(255, 255, 255, 0.2);
	animation: pulseGlow 4s ease-in-out infinite;
	transition: all 0.3s ease;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 6px;
	flex: 1;
	text-align: center;
	justify-content: center;
	padding: 8px;
	border-radius: 12px;
	transition: all 0.3s ease;
	cursor: pointer;
}

.stat-item:hover {
	background: rgba(255, 255, 255, 0.1);
	transform: translateY(-2px);
}

.stat-value {
	color: rgba(255, 255, 255, 0.98);
	font-size: 20px;
	font-weight: 800;
	text-shadow:
		0 2px 8px rgba(0, 0, 0, 0.3),
		0 0 20px rgba(255, 255, 255, 0.2);
	background: linear-gradient(135deg, #fff, #f0f8ff);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	animation: textGlow 3s ease-in-out infinite alternate;
}

.stat-label {
	color: rgba(255, 255, 255, 0.8);
	font-size: 13px;
	font-weight: 600;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
	letter-spacing: 0.5px;
}

.stat-divider {
	width: 1px;
	height: 30px;
	background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.3), transparent);
}

/* 现代化背景动画效果 */
@keyframes gradientFlow {
	0% {
		background-position: 0% 50%;
		filter: hue-rotate(0deg);
	}
	25% {
		background-position: 100% 50%;
		filter: hue-rotate(90deg);
	}
	50% {
		background-position: 100% 100%;
		filter: hue-rotate(180deg);
	}
	75% {
		background-position: 0% 100%;
		filter: hue-rotate(270deg);
	}
	100% {
		background-position: 0% 50%;
		filter: hue-rotate(360deg);
	}
}

@keyframes floatingOrbs {
	0%, 100% {
		transform: translate(0, 0) rotate(0deg);
		opacity: 0.8;
	}
	25% {
		transform: translate(20px, -30px) rotate(90deg);
		opacity: 0.6;
	}
	50% {
		transform: translate(-15px, -20px) rotate(180deg);
		opacity: 0.9;
	}
	75% {
		transform: translate(-25px, 15px) rotate(270deg);
		opacity: 0.7;
	}
}

@keyframes shimmerEffect {
	0% {
		background-position: -200% -200%;
	}
	100% {
		background-position: 200% 200%;
	}
}

@keyframes pulseGlow {
	0%, 100% {
		box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
	}
	50% {
		box-shadow: 0 0 40px rgba(255, 255, 255, 0.2);
	}
}

@keyframes textGlow {
	0% {
		text-shadow:
			0 2px 8px rgba(0, 0, 0, 0.3),
			0 0 20px rgba(255, 255, 255, 0.2);
	}
	100% {
		text-shadow:
			0 2px 8px rgba(0, 0, 0, 0.3),
			0 0 30px rgba(255, 255, 255, 0.4);
	}
}

/* 未登录状态用户信息样式 */
.unauth-user-section {
	margin: 30px 20px 36px 20px;
}

.unauth-user-info {
	display: flex;
	align-items: center;
	gap: 15px;
	padding: 10px 0;
}

.unauth-avatar {
	width: 60px;
	height: 60px;
	border-radius: 50%;
	overflow: hidden;
	background: rgba(255, 255, 255, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.unauth-avatar:hover {
	transform: scale(1.05);
}

.unauth-avatar-img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.unauth-text {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.unauth-title {
	font-size: 18px;
	font-weight: 600;
	color: rgba(255, 255, 255, 0.95);
}

.unauth-desc {
	font-size: 14px;
	color: rgba(255, 255, 255, 0.6);
}

.test-login-btn {
	margin-top: 8px;
	padding: 6px 12px;
	background: rgba(255, 255, 255, 0.2);
	color: white;
	border: 1px solid rgba(255, 255, 255, 0.3);
	border-radius: 6px;
	font-size: 12px;
	cursor: pointer;
}

.test-login-btn:hover {
	background: rgba(255, 255, 255, 0.3);
}

/* AI Mate 会员卡片 */
.member-card {
	margin: 20px 20px 24px 20px;
	background: linear-gradient(135deg, #00ff87 0%, #60efff 100%);
	border-radius: 16px;
	padding: 3px;
	position: relative;
	z-index: 1;
	animation: cardGlow 3s ease-in-out infinite;
}

@keyframes cardGlow {
	0%, 100% { box-shadow: 0 0 20px rgba(0, 255, 135, 0.3); }
	50% { box-shadow: 0 0 30px rgba(96, 239, 255, 0.5); }
}

.card-content {
	background: rgba(26, 26, 46, 0.9);
	border-radius: 14px;
	padding: 20px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.card-left {
	flex: 1;
}

.card-title {
	color: #00ff87;
	font-size: 18px;
	font-weight: bold;
	display: block;
	margin-bottom: 5px;
}

.card-desc {
	color: rgba(255, 255, 255, 0.7);
	font-size: 14px;
}

.upgrade-btn {
	background: linear-gradient(135deg, #00ff87 0%, #60efff 100%);
	color: #1a1a2e;
	border: none;
	padding: 10px 20px;
	border-radius: 20px;
	font-size: 14px;
	font-weight: bold;
	box-shadow: 0 4px 15px rgba(0, 255, 135, 0.3);
}



/* 功能模块 */
.function-modules {
	margin: 20px 20px 24px 20px;
	padding: 0;
	position: relative;
	z-index: 1;
}

.modules-container {
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(15px);
	border-radius: 20px;
	padding: 24px 20px;
	border: 1px solid rgba(255, 255, 255, 0.15);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.module-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20px;
}

.module-row:last-child {
	margin-bottom: 0;
}

.module-item {
	flex: 1;
	text-align: center;
	padding: 15px 10px;
	margin: 0 5px;
	background: rgba(255, 255, 255, 0.05);
	border-radius: 15px;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.module-item:first-child {
	margin-left: 0;
}

.module-item:last-child {
	margin-right: 0;
}

.module-item::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
	opacity: 0;
	transition: opacity 0.3s ease;
}

.module-item:active {
	transform: translateY(2px);
	background: rgba(255, 255, 255, 0.1);
}

.module-item:active::before {
	opacity: 1;
}

.item-icon {
	width: 50px;
	height: 50px;
	margin: 0 auto 12px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.icon-emoji {
	font-size: 24px;
}

.item-title {
	color: rgba(255, 255, 255, 0.9);
	font-size: 14px;
	font-weight: bold;
	display: block;
	margin-bottom: 5px;
}

.item-desc {
	color: rgba(255, 255, 255, 0.6);
	font-size: 11px;
}

/* 设置弹窗样式 */
.settings-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	backdrop-filter: blur(10px);
}

.settings-modal-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20px;
	width: 320px;
	max-width: 90%;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
	overflow: hidden;
	animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
	0% {
		opacity: 0;
		transform: scale(0.7) translateY(-30px) rotateX(10deg);
		filter: blur(10px);
	}
	50% {
		opacity: 0.8;
		transform: scale(1.02) translateY(-5px) rotateX(0deg);
		filter: blur(2px);
	}
	100% {
		opacity: 1;
		transform: scale(1) translateY(0) rotateX(0deg);
		filter: blur(0px);
	}
}

/* 添加光泽动画效果 */
@keyframes shimmer {
	0% {
		background-position: -200% 0;
	}
	100% {
		background-position: 200% 0;
	}
}

.settings-modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20px 20px 15px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-modal-title {
	color: white;
	font-size: 18px;
	font-weight: bold;
}

.settings-modal-close {
	width: 30px;
	height: 30px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.settings-modal-close:active {
	background: rgba(255, 255, 255, 0.2);
	transform: scale(0.95);
}

.close-icon {
	color: white;
	font-size: 16px;
	font-weight: bold;
}

.settings-modal-content {
	padding: 10px 0 20px;
}

.settings-option {
	display: flex;
	align-items: center;
	padding: 15px 20px;
	cursor: pointer;
	transition: all 0.3s ease;
	border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.settings-option:last-child {
	border-bottom: none;
}

.settings-option:active {
	background: rgba(255, 255, 255, 0.1);
	transform: scale(0.98);
}

.logout-option {
	border-top: 1px solid rgba(255, 255, 255, 0.1);
	margin-top: 10px;
}

.option-icon {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.15);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 15px;
}

.option-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.option-title {
	color: white;
	font-size: 16px;
	font-weight: 600;
	margin-bottom: 2px;
}

.option-desc {
	color: rgba(255, 255, 255, 0.7);
	font-size: 12px;
}

.option-arrow {
	width: 20px;
	height: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.arrow-icon {
	color: rgba(255, 255, 255, 0.6);
	font-size: 18px;
	font-weight: bold;
}

/* 钱包弹窗样式 */
.wallet-modal-overlay {
	position: fixed !important;
	top: 0 !important;
	left: 0 !important;
	right: 0 !important;
	bottom: 0 !important;
	background: rgba(0, 0, 0, 0.7) !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	z-index: 9999 !important;
	backdrop-filter: blur(15px) !important;
	animation: overlayFadeIn 0.3s ease-out !important;
}

@keyframes overlayFadeIn {
	from {
		opacity: 0;
		backdrop-filter: blur(0px);
	}
	to {
		opacity: 1;
		backdrop-filter: blur(15px);
	}
}

.wallet-modal-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%) !important;
	border-radius: 20px !important;
	width: 320px !important;
	max-width: 90% !important;
	min-height: 500px !important;
	max-height: 80vh !important;
	box-shadow:
		0 25px 50px rgba(0, 0, 0, 0.4),
		0 0 0 1px rgba(255, 255, 255, 0.1),
		inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
	overflow: hidden !important;
	animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
	position: relative !important;
}

.wallet-modal-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
	pointer-events: none;
}

.wallet-modal-card::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(
		90deg,
		transparent,
		rgba(255, 255, 255, 0.1),
		transparent
	);
	background-size: 200% 100%;
	animation: shimmer 4s infinite;
	pointer-events: none;
	opacity: 0.6;
}

.wallet-modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24px 24px 20px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.15);
	position: relative;
}

.wallet-modal-header::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 24px;
	right: 24px;
	height: 1px;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.wallet-modal-title {
	color: white;
	font-size: 20px;
	font-weight: 700;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
	letter-spacing: 0.5px;
}

.wallet-modal-close {
	width: 36px;
	height: 36px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.15);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.wallet-modal-close:active {
	background: rgba(255, 255, 255, 0.25);
	transform: scale(0.95);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.wallet-modal-content {
	padding: 16px 0 24px;
}

.wallet-balance-section {
	padding: 24px;
	text-align: center;
	border-bottom: 1px solid rgba(255, 255, 255, 0.15);
	margin-bottom: 20px;
	position: relative;
}

.wallet-balance-section::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 24px;
	right: 24px;
	height: 1px;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.balance-header {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 12px;
	gap: 12px;
}

.balance-label {
	color: rgba(255, 255, 255, 0.9);
	font-size: 15px;
	font-weight: 500;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.withdraw-bubble {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
	border-radius: 20px;
	padding: 10px 18px;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	z-index: 10;
	min-width: 90px;
	text-align: center;
	border: 1px solid rgba(255, 255, 255, 0.4);
	backdrop-filter: blur(10px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.withdraw-bubble:hover {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
	border-color: rgba(255, 255, 255, 0.6);
	transform: translateY(-1px);
	box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.withdraw-bubble:active {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.35), rgba(255, 255, 255, 0.25));
	transform: scale(0.96);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.bubble-text {
	color: white;
	font-size: 13px;
	font-weight: 600;
	pointer-events: none;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.balance-amount {
	color: white;
	font-size: 36px;
	font-weight: 800;
	display: block;
	margin-bottom: 8px;
	text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
	letter-spacing: 1px;
}

.balance-desc {
	color: rgba(255, 255, 255, 0.8);
	font-size: 13px;
	font-weight: 400;
	display: block;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.wallet-option {
	display: flex;
	align-items: center;
	padding: 15px 20px;
	cursor: pointer;
	transition: all 0.3s ease;
	border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.wallet-option:last-child {
	border-bottom: none;
}

.wallet-option:active {
	background: rgba(255, 255, 255, 0.1);
	transform: scale(0.98);
}

/* 记录选项卡样式 */
.records-tabs {
	display: flex;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 16px;
	margin: 20px 24px 16px;
	padding: 4px;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 12px 16px;
	border-radius: 12px;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	cursor: pointer;
	position: relative;
}

.tab-item.active {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	transform: translateY(-1px);
}

.tab-text {
	color: rgba(255, 255, 255, 0.85);
	font-size: 15px;
	font-weight: 500;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.tab-item.active .tab-text {
	color: white;
	font-weight: 700;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 余额明细样式 */
.balance-history-section {
	padding: 0 24px 16px;
}

/* 提现记录样式 */
.withdraw-history-section {
	padding: 0 24px 16px;
}

.history-header {
	padding: 16px 0;
	border-bottom: 1px solid rgba(255, 255, 255, 0.15);
	margin-bottom: 12px;
	position: relative;
}

.history-header::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 1px;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.history-title {
	color: white;
	font-size: 17px;
	font-weight: 700;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.history-list {
	max-height: 240px;
	overflow-y: auto;
	padding-right: 4px;
}

.history-list::-webkit-scrollbar {
	width: 4px;
}

.history-list::-webkit-scrollbar-track {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 2px;
}

.history-list::-webkit-scrollbar-thumb {
	background: rgba(255, 255, 255, 0.3);
	border-radius: 2px;
}

.history-list::-webkit-scrollbar-thumb:hover {
	background: rgba(255, 255, 255, 0.5);
}

.loading-history {
	text-align: center;
	padding: 40px 0;
}

.loading-text {
	color: rgba(255, 255, 255, 0.8);
	font-size: 15px;
	font-weight: 500;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.empty-history {
	text-align: center;
	padding: 40px 0;
}

.empty-text {
	color: rgba(255, 255, 255, 0.6);
	font-size: 15px;
	font-weight: 400;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.history-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16px 0;
	border-bottom: 1px solid rgba(255, 255, 255, 0.08);
	transition: all 0.3s ease;
	border-radius: 8px;
	margin-bottom: 4px;
}

.history-item:hover {
	background: rgba(255, 255, 255, 0.05);
	padding-left: 8px;
	padding-right: 8px;
}

.history-item:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

.history-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.history-type {
	color: white;
	font-size: 15px;
	font-weight: 600;
	margin-bottom: 2px;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.history-time {
	color: rgba(255, 255, 255, 0.7);
	font-size: 13px;
	font-weight: 400;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.history-amount {
	font-size: 17px;
	font-weight: 700;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
	letter-spacing: 0.5px;
}

.history-amount.income {
	color: #10b981;
	text-shadow: 0 0 8px rgba(16, 185, 129, 0.3);
}

.history-amount.expense {
	color: #ef4444;
	text-shadow: 0 0 8px rgba(239, 68, 68, 0.3);
}

/* 提现记录项样式 */
.withdraw-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16px 0;
	border-bottom: 1px solid rgba(255, 255, 255, 0.08);
	transition: all 0.3s ease;
	border-radius: 8px;
	margin-bottom: 4px;
}

.withdraw-item:hover {
	background: rgba(255, 255, 255, 0.05);
	padding-left: 8px;
	padding-right: 8px;
}

.withdraw-item:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

.withdraw-info {
	display: flex;
	flex-direction: column;
	gap: 6px;
	flex: 1;
}

.withdraw-type {
	color: white;
	font-size: 14px;
	font-weight: 500;
}

.withdraw-time {
	color: rgba(255, 255, 255, 0.6);
	font-size: 12px;
}

/* 原有的withdraw-details样式保留，以防其他地方使用 */
.withdraw-details {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 4px;
}

.withdraw-amount {
	color: white;
	font-size: 14px;
	font-weight: bold;
}

.withdraw-status {
	font-size: 12px;
	padding: 2px 8px;
	border-radius: 10px;
	font-weight: 500;
}

.status-pending {
	background: rgba(255, 193, 7, 0.2);
	color: #ffc107;
}

.status-processing {
	background: rgba(0, 123, 255, 0.2);
	color: #007bff;
}

.status-completed {
	background: rgba(40, 167, 69, 0.2);
	color: #28a745;
}

.status-rejected {
	background: rgba(220, 53, 69, 0.2);
	color: #dc3545;
}

.status-default {
	background: rgba(255, 255, 255, 0.1);
	color: rgba(255, 255, 255, 0.8);
}

/* 提现表单样式 */
.withdraw-form-section {
	padding: 20px;
}

/* 错误信息样式 */
.error-message {
	background: rgba(255, 59, 48, 0.1);
	border: 1px solid rgba(255, 59, 48, 0.3);
	border-radius: 8px;
	padding: 12px 16px;
	margin-bottom: 16px;
}

.error-text {
	color: #ff3b30;
	font-size: 14px;
	line-height: 1.4;
}



.withdraw-form {
	display: flex;
	flex-direction: column;
	gap: 15px;
}

.form-item {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.form-label {
	color: rgba(255, 255, 255, 0.9);
	font-size: 14px;
	font-weight: 500;
}

.amount-display {
	display: flex;
	align-items: baseline;
	gap: 8px;
}

.amount-text {
	color: white;
	font-size: 24px;
	font-weight: bold;
}

.amount-note {
	color: rgba(255, 255, 255, 0.6);
	font-size: 12px;
}

.fee-text {
	color: rgba(255, 255, 255, 0.8);
	font-size: 14px;
}

.actual-amount {
	color: #4ade80;
	font-size: 18px;
	font-weight: bold;
}

.form-input {
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: 8px;
	padding: 12px;
	color: white;
	font-size: 14px;
}

.form-input::placeholder {
	color: rgba(255, 255, 255, 0.5);
}

.wechat-note {
	background: rgba(76, 175, 80, 0.1);
	border: 1px solid rgba(76, 175, 80, 0.3);
	border-radius: 8px;
	padding: 12px;
}

.note-text {
	color: #4ade80;
	font-size: 14px;
}

.form-actions {
	display: flex;
	gap: 10px;
	margin-top: 10px;
}

.action-button {
	flex: 1;
	padding: 12px;
	border-radius: 8px;
	text-align: center;
	font-size: 16px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.cancel-btn {
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	color: rgba(255, 255, 255, 0.8);
}

.cancel-btn:active {
	background: rgba(255, 255, 255, 0.2);
	transform: scale(0.98);
}

.submit-btn {
	background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
	border: none;
	color: white;
}

.submit-btn:active {
	transform: scale(0.98);
}

.submit-btn.disabled {
	background: rgba(255, 255, 255, 0.1);
	color: rgba(255, 255, 255, 0.4);
	cursor: not-allowed;
}

/* 三列布局样式 */
.withdraw-row {
	display: flex;
	align-items: center;
	margin-top: 8px;
	width: 100%;
}

.withdraw-left {
	flex: 1;
	min-width: 20px;
}

.withdraw-middle {
	flex: 2;
	display: flex;
	justify-content: center;
	padding: 0 5px;
}

.withdraw-right {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 4px;
}

/* 驳回原因样式 */
.reject-reason {
	padding: 4px 8px;
	background: rgba(255, 59, 48, 0.1);
	border: 1px solid rgba(255, 59, 48, 0.2);
	border-radius: 6px;
	max-width: 100%;
	text-align: center;
}

.reject-reason-text {
	color: #ff8a8a;
	font-size: 12px;
	line-height: 1.4;
}

/* 套餐弹窗样式 */
.package-history-section {
	padding: 20px;
	max-height: 400px;
	overflow-y: auto;
}

.package-item {
	background: rgba(255, 255, 255, 0.05);
	border-radius: 12px;
	padding: 16px;
	margin-bottom: 12px;
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.package-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
}

.package-name {
	color: white;
	font-size: 16px;
	font-weight: bold;
}

.package-status {
	padding: 4px 12px;
	border-radius: 12px;
	font-size: 12px;
	font-weight: 500;
}

.package-status.active {
	background: rgba(52, 199, 89, 0.2);
	color: #34c759;
}

.package-status.expired {
	background: rgba(255, 59, 48, 0.2);
	color: #ff3b30;
}

.package-details {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.package-detail-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 3px;
}

.detail-label {
	color: rgba(255, 255, 255, 0.7);
	font-size: 14px;
	flex-shrink: 0;
	min-width: 80px;
}

.detail-value {
	color: white;
	font-size: 14px;
	font-weight: 500;
	text-align: right;
	word-break: break-all;
	flex: 1;
	margin-left: 10px;
}

.remaining-days {
	color: #34c759;
	font-weight: bold;
}

.order-no {
	font-family: monospace;
	color: #E3F2FD !important;
	font-size: 12px;
}

.package-amount {
	color: #FFD700 !important;
	font-weight: bold;
}

/* 点数弹窗样式 */
.points-summary {
	background: rgba(255, 255, 255, 0.05);
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 20px;
	text-align: center;
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.points-label {
	font-size: 14px;
	color: #cccccc;
	display: block;
	margin-bottom: 8px;
}

.points-value {
	font-size: 24px;
	font-weight: 600;
	color: #4CAF50;
}

.points-history {
	padding: 0;
}

.history-title {
	font-size: 16px;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 16px;
}

.points-item {
	background: rgba(255, 255, 255, 0.05);
	border-radius: 8px;
	padding: 12px;
	margin-bottom: 8px;
	border: 1px solid rgba(255, 255, 255, 0.1);
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.points-info {
	flex: 1;
}

.points-type-row {
	display: flex;
	align-items: center;
	margin-bottom: 4px;
}

.points-type-icon {
	font-size: 16px;
	margin-right: 8px;
}

.points-type {
	color: white;
	font-size: 14px;
	font-weight: 500;
}

.app-title {
	color: #4CAF50;
	font-size: 12px;
	font-weight: 600;
	margin-top: 2px;
	display: block;
}

.points-time {
	color: #cccccc;
	font-size: 12px;
}

.points-amount-section {
	text-align: right;
}

.points-amount {
	font-size: 16px;
	font-weight: bold;
	display: block;
	margin-bottom: 2px;
}

.points-amount.income {
	color: #4CAF50;
}

.points-amount.expense {
	color: #FF5722;
}

.points-balance {
	color: #cccccc;
	font-size: 11px;
}

/* 海报弹窗样式 */
.poster-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.7);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.poster-modal {
	background: #ffffff;
	border-radius: 16px;
	width: 90%;
	max-width: 400px;
	max-height: 80vh;
	overflow: hidden;
	position: relative;
}

.poster-modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	border-bottom: 1px solid #f0f0f0;
}

.poster-modal-title {
	font-size: 18px;
	font-weight: bold;
	color: #333333;
}

.poster-modal-close {
	font-size: 24px;
	color: #999999;
	cursor: pointer;
	width: 30px;
	height: 30px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.poster-modal-content {
	padding: 20px;
	max-height: 60vh;
	overflow-y: auto;
}

.poster-loading {
	text-align: center;
	padding: 40px 20px;
}

.loading-text {
	color: #666666;
	font-size: 16px;
}

.poster-preview {
	text-align: center;
}

.poster-container {
	position: relative;
	border-radius: 12px;
	overflow: hidden; /* 改回 hidden 确保背景图片正确显示 */
	margin-bottom: 20px;
	background-color: #f5f5f5;
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
	/* 移除固定的最小高度，让高度完全由 posterContainerStyle() 方法控制 */
	/* 确保容器有明确的尺寸 */
	display: block;
}

.poster-background {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	object-fit: cover;
	z-index: 3;
}

.default-background {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
	z-index: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.background-content {
	text-align: center;
	color: white;
}

.app-title {
	font-size: 24px;
	font-weight: bold;
	margin-bottom: 10px;
	display: block;
}

.invite-title {
	font-size: 16px;
	opacity: 0.9;
	display: block;
}

.invite-code {
	font-size: 14px;
	color: rgba(255, 255, 255, 0.8);
	margin-top: 12px;
	padding: 8px 16px;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 20px;
	border: 1px solid rgba(255, 255, 255, 0.3);
	display: block;
}

.qrcode-overlay {
	position: absolute;
	background: white;
	border-radius: 8px;
	padding: 4px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.2);
	z-index: 20;
	/* 其他样式通过 :style 动态设置 */
}

.qrcode-image {
	width: 100%;
	height: 100%;
	object-fit: contain;
	border-radius: 4px;
}

.qrcode-placeholder {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	background: #f8f9fa;
	border: 2px dashed #dee2e6;
	border-radius: 4px;
}

.placeholder-text {
	font-size: 12px;
	color: #6c757d;
	margin-bottom: 4px;
}

.placeholder-code {
	font-size: 10px;
	color: #495057;
	font-weight: bold;
}

.poster-error {
	text-align: center;
	padding: 40px 20px;
}

.error-icon {
	font-size: 48px;
	margin-bottom: 16px;
}

.error-text {
	font-size: 16px;
	color: #333333;
	font-weight: bold;
	margin-bottom: 8px;
	display: block;
}

.error-desc {
	font-size: 14px;
	color: #666666;
	margin-bottom: 20px;
	display: block;
}

.retry-btn {
	background: #007AFF;
	color: white;
	border: none;
	border-radius: 8px;
	padding: 12px 24px;
	font-size: 16px;
}

.invite-info {
	margin-bottom: 20px;
}

.invite-item {
	display: flex;
	align-items: center;
	margin-bottom: 12px;
	padding: 12px;
	background: #f8f9fa;
	border-radius: 8px;
}

.invite-label {
	font-size: 14px;
	color: #666666;
	min-width: 80px;
}

.invite-value {
	flex: 1;
	font-size: 14px;
	color: #333333;
	margin: 0 10px;
	word-break: break-all;
}

.copy-btn {
	background: #007AFF;
	color: #ffffff;
	padding: 6px 12px;
	border-radius: 4px;
	font-size: 12px;
	cursor: pointer;
}

.poster-actions {
	display: flex;
	gap: 12px;
}

.action-btn {
	flex: 1;
	padding: 12px;
	border-radius: 8px;
	font-size: 16px;
	border: none;
	cursor: pointer;
}

.save-btn {
	background: #34C759;
	color: #ffffff;
}

.share-btn {
	background: #007AFF;
	color: #ffffff;
}
</style>
