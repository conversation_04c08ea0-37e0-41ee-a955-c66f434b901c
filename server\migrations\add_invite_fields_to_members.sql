-- 为members表添加邀请码和二维码字段
-- 检查字段是否已存在，如果不存在则添加

-- 添加邀请码字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'members' 
     AND table_schema = DATABASE()
     AND column_name = 'invite_code') > 0,
    'SELECT "invite_code字段已存在" as message;',
    'ALTER TABLE members ADD COLUMN invite_code VARCHAR(50) NULL UNIQUE AFTER referrer_id;'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加邀请链接字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'members' 
     AND table_schema = DATABASE()
     AND column_name = 'invite_url') > 0,
    'SELECT "invite_url字段已存在" as message;',
    'ALTER TABLE members ADD COLUMN invite_url TEXT NULL AFTER invite_code;'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加二维码路径字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'members' 
     AND table_schema = DATABASE()
     AND column_name = 'qr_code_path') > 0,
    'SELECT "qr_code_path字段已存在" as message;',
    'ALTER TABLE members ADD COLUMN qr_code_path VARCHAR(255) NULL AFTER invite_url;'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加二维码URL字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'members' 
     AND table_schema = DATABASE()
     AND column_name = 'qr_code_url') > 0,
    'SELECT "qr_code_url字段已存在" as message;',
    'ALTER TABLE members ADD COLUMN qr_code_url TEXT NULL AFTER qr_code_path;'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示表结构确认
DESCRIBE members;
